#!/usr/bin/env python3
"""
Evaluate Advanced ML Model Performance
Compare different models and show improvement metrics
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.model_selection import TimeSeriesSplit

def load_model_info():
    """Load model information from JSON files"""
    models_info = {}
    
    # Check for advanced models
    for model_file in os.listdir("ml_models"):
        if model_file.endswith("_model_info.json"):
            try:
                with open(f"ml_models/{model_file}", 'r') as f:
                    info = json.load(f)
                    model_name = model_file.replace("_model_info.json", "")
                    models_info[model_name] = info
            except Exception as e:
                print(f"⚠️  Error loading {model_file}: {e}")
    
    return models_info

def evaluate_model_on_data(model_path, scaler_path, data_path, feature_cols):
    """Evaluate a specific model on test data"""
    try:
        # Load model and scaler
        model = joblib.load(model_path)
        scaler = joblib.load(scaler_path)
        
        # Load test data
        df = pd.read_csv(data_path)
        
        # Prepare features
        available_features = [col for col in feature_cols if col in df.columns]
        if len(available_features) < len(feature_cols) * 0.8:  # At least 80% features available
            print(f"⚠️  Only {len(available_features)}/{len(feature_cols)} features available")
            return None
        
        # Clean data
        df_clean = df[available_features + ['target']].dropna()
        if len(df_clean) < 100:
            print(f"⚠️  Insufficient clean data: {len(df_clean)} samples")
            return None
        
        X = df_clean[available_features]
        y = df_clean['target']
        
        # Scale features
        X_scaled = scaler.transform(X)
        
        # Make predictions
        y_pred = model.predict(X_scaled)
        y_proba = model.predict_proba(X_scaled)
        
        # Calculate metrics
        accuracy = accuracy_score(y, y_pred)
        
        # Time series validation
        tscv = TimeSeriesSplit(n_splits=5)
        cv_scores = []
        for train_idx, test_idx in tscv.split(X_scaled):
            X_train, X_test = X_scaled[train_idx], X_scaled[test_idx]
            y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
            
            model.fit(X_train, y_train)
            score = model.score(X_test, y_test)
            cv_scores.append(score)
        
        return {
            'accuracy': accuracy,
            'cv_mean': np.mean(cv_scores),
            'cv_std': np.std(cv_scores),
            'samples': len(df_clean),
            'features': len(available_features),
            'predictions': y_pred,
            'probabilities': y_proba,
            'actual': y.values
        }
        
    except Exception as e:
        print(f"❌ Error evaluating model: {e}")
        return None

def compare_models():
    """Compare all available models"""
    print("🔍 ADVANCED MODEL EVALUATION")
    print("=" * 60)
    
    models_info = load_model_info()
    
    if not models_info:
        print("❌ No model info files found!")
        return
    
    print(f"📊 Found {len(models_info)} model(s):")
    for name, info in models_info.items():
        print(f"   • {name}: {info.get('accuracy', 0):.2%} accuracy")
    
    # Check if training data exists
    data_files = [
        "data/training_data_90days_advanced.csv",
        "data/training_data_90days.csv",
        "data/fallback.csv"
    ]
    
    test_data = None
    for data_file in data_files:
        if os.path.exists(data_file):
            test_data = data_file
            break
    
    if not test_data:
        print("❌ No test data found!")
        return
    
    print(f"📁 Using test data: {test_data}")
    
    # Evaluate each model
    results = {}
    
    for model_name, info in models_info.items():
        print(f"\n🤖 Evaluating {model_name}...")
        
        model_file = f"ml_models/{model_name}_classifier.pkl"
        scaler_file = f"ml_models/{model_name}_scaler.pkl"
        
        # Handle different naming conventions
        if not os.path.exists(model_file):
            if model_name == "real_data":
                model_file = "ml_models/real_data_classifier.pkl"
                scaler_file = "ml_models/real_data_scaler.pkl"
            else:
                continue
        
        if not os.path.exists(model_file) or not os.path.exists(scaler_file):
            print(f"   ❌ Model files not found")
            continue
        
        # Get feature columns from model info
        feature_cols = info.get('feature_list', [
            'RSI', 'MACD', 'VWAP',
            'rsi_oversold', 'rsi_overbought', 'rsi_momentum',
            'macd_signal', 'macd_momentum',
            'price_vs_vwap', 'vwap_distance',
            'price_range', 'volume_ratio',
            'trend_short', 'trend_long'
        ])
        
        result = evaluate_model_on_data(model_file, scaler_file, test_data, feature_cols)
        
        if result:
            results[model_name] = result
            print(f"   ✅ Accuracy: {result['accuracy']:.2%}")
            print(f"   📈 CV Score: {result['cv_mean']:.2%} ± {result['cv_std']:.2%}")
            print(f"   📊 Samples: {result['samples']}, Features: {result['features']}")
        else:
            print(f"   ❌ Evaluation failed")
    
    # Summary
    if results:
        print(f"\n🏆 MODEL RANKING:")
        sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy'], reverse=True)
        
        for i, (name, result) in enumerate(sorted_results, 1):
            print(f"   {i}. {name}: {result['accuracy']:.2%} accuracy")
        
        best_model = sorted_results[0]
        print(f"\n🎯 BEST MODEL: {best_model[0]} with {best_model[1]['accuracy']:.2%} accuracy")
        
        # Performance improvement
        if len(sorted_results) > 1:
            improvement = best_model[1]['accuracy'] - sorted_results[-1][1]['accuracy']
            print(f"🚀 Improvement over worst: {improvement:.2%}")
    
    else:
        print("\n❌ No models could be evaluated!")

def generate_performance_report():
    """Generate detailed performance report"""
    print("\n📋 GENERATING PERFORMANCE REPORT...")
    
    models_info = load_model_info()
    
    report = {
        'evaluation_date': datetime.now().isoformat(),
        'models_evaluated': len(models_info),
        'best_accuracy': 0,
        'recommendations': []
    }
    
    if models_info:
        # Find best accuracy
        best_accuracy = max([info.get('accuracy', 0) for info in models_info.values()])
        report['best_accuracy'] = best_accuracy
        
        # Generate recommendations
        if best_accuracy > 0.80:
            report['recommendations'].append("✅ Excellent model performance (>80%)")
        elif best_accuracy > 0.75:
            report['recommendations'].append("✅ Good model performance (>75%)")
        elif best_accuracy > 0.70:
            report['recommendations'].append("⚠️  Fair model performance (>70%) - consider improvements")
        else:
            report['recommendations'].append("❌ Poor model performance (<70%) - needs improvement")
        
        # Feature recommendations
        for name, info in models_info.items():
            feature_count = info.get('features', 0)
            if feature_count < 10:
                report['recommendations'].append(f"💡 {name}: Add more features (currently {feature_count})")
            elif feature_count > 50:
                report['recommendations'].append(f"💡 {name}: Consider feature selection (currently {feature_count})")
    
    # Save report
    with open("ml_models/performance_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ Performance report saved to ml_models/performance_report.json")
    
    return report

def main():
    """Main evaluation function"""
    compare_models()
    report = generate_performance_report()
    
    print(f"\n📊 SUMMARY:")
    print(f"   • Models evaluated: {report['models_evaluated']}")
    print(f"   • Best accuracy: {report['best_accuracy']:.2%}")
    print(f"   • Recommendations: {len(report['recommendations'])}")
    
    print(f"\n💡 TOP RECOMMENDATIONS:")
    for rec in report['recommendations'][:3]:
        print(f"   {rec}")

if __name__ == "__main__":
    main()
