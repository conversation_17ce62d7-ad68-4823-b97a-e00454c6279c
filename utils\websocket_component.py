import streamlit as st
import streamlit.components.v1 as components
import json

def create_websocket_price_ticker(symbol, instrument_token):
    """Create a client-side WebSocket price ticker that updates without page refresh"""
    
    # HTML/JS for WebSocket price ticker
    websocket_html = f"""
    <div id="ws-price-ticker" class="price-ticker">
        <div class="ticker-symbol">{symbol}</div>
        <div class="ticker-price">Loading...</div>
        <div class="ticker-change">--</div>
    </div>
    
    <style>
        .price-ticker {{
            font-family: 'Roboto', sans-serif;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .ticker-symbol {{
            font-size: 14px;
            color: #555;
            margin-bottom: 5px;
        }}
        .ticker-price {{
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .ticker-change {{
            font-size: 14px;
        }}
        .price-up {{
            color: #00C851;
        }}
        .price-down {{
            color: #FF4444;
        }}
    </style>
    
    <script>
        // Function to update the price ticker
        function updatePriceTicker(data) {{
            const tickerElement = document.getElementById('ws-price-ticker');
            if (!tickerElement) return;
            
            const priceElement = tickerElement.querySelector('.ticker-price');
            const changeElement = tickerElement.querySelector('.ticker-change');
            
            if (data.price) {{
                priceElement.textContent = '₹' + parseFloat(data.price).toFixed(2);
                
                // Update price color based on change
                if (data.change > 0) {{
                    priceElement.className = 'ticker-price price-up';
                    changeElement.className = 'ticker-change price-up';
                    changeElement.textContent = '+' + data.change.toFixed(2) + '%';
                }} else if (data.change < 0) {{
                    priceElement.className = 'ticker-price price-down';
                    changeElement.className = 'ticker-change price-down';
                    changeElement.textContent = data.change.toFixed(2) + '%';
                }} else {{
                    priceElement.className = 'ticker-price';
                    changeElement.className = 'ticker-change';
                    changeElement.textContent = '0.00%';
                }}
                
                // Flash effect on price change
                tickerElement.style.transition = 'background-color 0.3s';
                tickerElement.style.backgroundColor = data.change >= 0 ? 'rgba(0, 200, 81, 0.1)' : 'rgba(255, 68, 68, 0.1)';
                setTimeout(() => {{
                    tickerElement.style.backgroundColor = '#f8f9fa';
                }}, 300);
            }}
        }}
        
        // Simulate WebSocket for demo (in production, connect to real WebSocket)
        function simulateWebSocket() {{
            // This would be replaced with actual WebSocket connection
            const basePrice = 18000 + Math.random() * 1000;
            let lastPrice = basePrice;
            
            setInterval(() => {{
                const newPrice = lastPrice + (Math.random() - 0.5) * 10;
                const change = ((newPrice - basePrice) / basePrice) * 100;
                
                updatePriceTicker({{
                    price: newPrice,
                    change: change
                }});
                
                lastPrice = newPrice;
            }}, 1000);
        }}
        
        // Start the simulation
        simulateWebSocket();
        
        // In production, you would use:
        /*
        const ws = new WebSocket('wss://your-websocket-server');
        ws.onmessage = (event) => {{
            const data = JSON.parse(event.data);
            if (data.instrument_token == {instrument_token}) {{
                updatePriceTicker({{
                    price: data.last_price,
                    change: data.change
                }});
            }}
        }};
        */
    </script>
    """
    
    # Render the HTML/JS component
    components.html(websocket_html, height=100)

def create_live_chart_updater(chart_div_id="chart-container"):
    """Create a JavaScript component that can update a Plotly chart without page refresh"""
    
    js_code = f"""
    <script>
        // Function to update a Plotly chart with new data
        function updatePlotlyChart(chartDivId, newData) {{
            const chartDiv = document.getElementById(chartDivId);
            if (!chartDiv || !window.Plotly) return;
            
            // Get the existing Plotly chart
            const plotlyChart = chartDiv.data;
            if (!plotlyChart) return;
            
            // Update the candlestick data
            Plotly.restyle(chartDiv, {{
                'open': [newData.open],
                'high': [newData.high],
                'low': [newData.low],
                'close': [newData.close]
            }}, 0);
            
            // Update volume if present
            if (plotlyChart.length > 1) {{
                Plotly.restyle(chartDiv, {{
                    'y': [newData.volume]
                }}, 1);
            }}
        }}
        
        // In production, connect this to WebSocket data
        // For now, we'll just expose the function globally
        window.updatePlotlyChart = updatePlotlyChart;
    </script>
    """
    
    # Render the JavaScript
    components.html(js_code, height=0)