#!/usr/bin/env python3
"""
Test Data Flow for Live Dashboard
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simulated_data():
    """Test the simulated data generation"""
    print("🧪 Testing Simulated Data Generation")
    print("=" * 40)
    
    try:
        # Import the function
        from live_dashboard import get_simulated_data
        
        # Test for NIFTY
        print("📊 Testing NIFTY data...")
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is not None and not candles.empty:
            print(f"✅ Generated {len(candles)} candles")
            print(f"   Columns: {list(candles.columns)}")
            print(f"   Latest close: {candles['close'].iloc[-1]:.2f}")
            print(f"   Price range: {candles['close'].min():.2f} - {candles['close'].max():.2f}")
            
            # Check for required columns
            required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in candles.columns]
            if missing_cols:
                print(f"⚠️ Missing columns: {missing_cols}")
            else:
                print("✅ All required columns present")
                
            # Check for indicators
            indicator_cols = ['RSI', 'MACD', 'VWAP']
            present_indicators = [col for col in indicator_cols if col in candles.columns]
            print(f"📈 Indicators present: {present_indicators}")
            
            return True
        else:
            print("❌ No data generated")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ml_prediction():
    """Test ML prediction with simulated data"""
    print("\n🧪 Testing ML Prediction")
    print("=" * 40)
    
    try:
        # Import required functions
        from live_dashboard import get_simulated_data
        from ml_models.predictor import predict_ce_pe
        
        # Get test data
        print("📊 Getting test data...")
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is None or candles.empty:
            print("❌ No test data available")
            return False
            
        print(f"✅ Got {len(candles)} candles")
        
        # Test ML prediction
        print("🤖 Testing ML prediction...")
        prediction = predict_ce_pe(candles)
        
        if prediction:
            print("✅ ML prediction successful!")
            print(f"   Signal: {prediction.get('signal', 'Unknown')}")
            print(f"   Confidence: {prediction.get('confidence', 0):.1f}%")
            print(f"   Current Price: {prediction.get('current_price', 0):.2f}")
            print(f"   Stop Loss: {prediction.get('stoploss', 0):.2f}")
            print(f"   Target: {prediction.get('target', 0):.2f}")
            print(f"   Trend: {prediction.get('trend', 'Unknown')}")
            return True
        else:
            print("❌ ML prediction failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_creation():
    """Test chart creation with simulated data"""
    print("\n🧪 Testing Chart Creation")
    print("=" * 40)
    
    try:
        # Import required functions
        from live_dashboard import get_simulated_data
        from ml_models.predictor import predict_ce_pe
        from utils.chart_utils import create_zerodha_style_chart
        from pattern_engine import detect_patterns, calculate_support_resistance
        
        # Get test data
        print("📊 Getting test data...")
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is None or candles.empty:
            print("❌ No test data available")
            return False
            
        print(f"✅ Got {len(candles)} candles")
        
        # Get ML prediction
        print("🤖 Getting ML prediction...")
        ml_prediction = predict_ce_pe(candles)
        print(f"✅ ML prediction: {ml_prediction.get('signal', 'Unknown') if ml_prediction else 'Failed'}")
        
        # Detect patterns
        print("🔍 Detecting patterns...")
        patterns = detect_patterns(candles)
        support_levels, resistance_levels = calculate_support_resistance(candles)
        print(f"✅ Found {len(patterns)} patterns, {len(support_levels)} support, {len(resistance_levels)} resistance")
        
        # Create chart
        print("📈 Creating Zerodha-style chart...")
        fig = create_zerodha_style_chart(
            candles.tail(50),
            patterns=patterns,
            support_levels=support_levels,
            resistance_levels=resistance_levels,
            ml_prediction=ml_prediction,
            show_indicators=True
        )
        
        print("✅ Chart created successfully!")
        
        # Save chart for inspection
        print("💾 Saving chart...")
        fig.write_html("test_data_flow_chart.html")
        print("✅ Chart saved as 'test_data_flow_chart.html'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indicators():
    """Test indicator calculation"""
    print("\n🧪 Testing Indicator Calculation")
    print("=" * 40)
    
    try:
        # Import required functions
        from utils.indicators import calculate_indicators
        import pandas as pd
        import numpy as np
        
        # Create simple test data
        print("📊 Creating test data...")
        dates = pd.date_range(start=datetime.now() - timedelta(days=1), periods=50, freq='5min')
        prices = 22000 + np.cumsum(np.random.normal(0, 10, 50))
        
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': prices * 1.002,
            'low': prices * 0.998,
            'close': prices,
            'volume': np.random.randint(10000, 50000, 50)
        })
        
        print(f"✅ Created {len(df)} test candles")
        
        # Calculate indicators
        print("📈 Calculating indicators...")
        df_with_indicators = calculate_indicators(df)
        
        # Check indicators
        expected_indicators = ['RSI', 'MACD', 'VWAP', 'EMA_12', 'SMA_20']
        present_indicators = [ind for ind in expected_indicators if ind in df_with_indicators.columns]
        missing_indicators = [ind for ind in expected_indicators if ind not in df_with_indicators.columns]
        
        print(f"✅ Present indicators: {present_indicators}")
        if missing_indicators:
            print(f"⚠️ Missing indicators: {missing_indicators}")
        
        # Check for NaN values
        nan_counts = df_with_indicators[present_indicators].isna().sum()
        print(f"📊 NaN counts: {dict(nan_counts)}")
        
        return len(present_indicators) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 TESTING DATA FLOW FOR LIVE DASHBOARD")
    print("=" * 60)
    
    # Run all tests
    test_results = {
        'Simulated Data': test_simulated_data(),
        'Indicators': test_indicators(),
        'ML Prediction': test_ml_prediction(),
        'Chart Creation': test_chart_creation()
    }
    
    # Summary
    print(f"\n📊 TEST SUMMARY:")
    print("=" * 30)
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    all_passed = all(test_results.values())
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   The data flow should work correctly in the dashboard.")
        print(f"   Check 'test_data_flow_chart.html' to see the generated chart.")
    else:
        print(f"\n⚠️ SOME TESTS FAILED!")
        print(f"   Check the error messages above to identify issues.")
        failed_tests = [name for name, result in test_results.items() if not result]
        print(f"   Failed tests: {failed_tests}")

if __name__ == "__main__":
    main()
