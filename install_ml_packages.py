#!/usr/bin/env python3
"""
Install Required ML Packages for Advanced Trading Model
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """Install a package using pip"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} is already installed")
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} installed successfully")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package_name}")
            return False

def main():
    """Install all required packages for advanced ML model"""
    print("🚀 INSTALLING ADVANCED ML PACKAGES")
    print("=" * 50)
    
    packages = [
        ("xgboost", "xgboost"),
        ("lightgbm", "lightgbm"), 
        ("catboost", "catboost"),
        ("tensorflow", "tensorflow"),
        ("optuna", "optuna"),
        ("scikit-learn", "sklearn"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("joblib", "joblib")
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    print(f"\n📊 INSTALLATION SUMMARY:")
    print(f"   ✅ Successful: {success_count}/{total_count}")
    print(f"   ❌ Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print(f"\n🎉 All packages installed successfully!")
        print(f"🚀 You can now run: python train_with_real_data.py")
    else:
        print(f"\n⚠️  Some packages failed to install. Please install manually:")
        for package_name, import_name in packages:
            try:
                importlib.import_module(import_name)
            except ImportError:
                print(f"   pip install {package_name}")

if __name__ == "__main__":
    main()
