# 🚀 Live Options Trading Dashboard

A comprehensive real-time trading dashboard with ML predictions, pattern recognition, and live market data streaming.

## ✨ Features

| Feature | Description |
|---------|-------------|
| 📡 **Live Market Data** | Real-time price streaming via Kite WebSocket |
| ⏱️ **Auto-Refresh Dashboard** | Updates every 1-5 seconds without manual intervention |
| 🧠 **ML Predictions** | CE/PE signals with confidence scores |
| 🧩 **16 Chart Patterns** | Complete pattern detection (Head & Shoulders, Triangles, Flags, etc.) |
| 📈 **Interactive Charts** | Live candlestick charts with pattern overlays |
| 🔻 **Support/Resistance** | Auto-calculated and drawn live levels |
| 🖥️ **3-Column Layout** | Controls | Chart | Signals & Analysis |
| 🎯 **Smart SL/TP** | ML-based stop loss and target calculation |

## 🏗️ Architecture

```
📦 Trading Dashboard
├── 🔌 WebSocket Engine (kite_websocket.py)
├── 🧱 Pattern Detection (pattern_engine.py)
├── 🧠 ML Predictor (ml_models/predictor.py)
├── 📊 Technical Indicators (utils/indicators.py)
├── 📈 Chart Visualization (utils/chart_utils.py)
└── 🖥️ Live Dashboard (live_dashboard.py)
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Setup Kite Authentication
```bash
# Run auto token generator
python auto_token.py
```
This will create `enctoken.txt` with your authentication token.

### 3. Test the System
```bash
# Run comprehensive tests
python test_system.py
```

### 4. Launch Live Dashboard
```bash
# Recommended: Use the optimized startup script
python start.py
```
Or manually:
```bash
# Start the live trading dashboard
streamlit run live_dashboard.py
```

**✅ Auto-refresh is now enabled by default for the best experience!**
**🔄 Real-time updates every 3 seconds with WebSocket-only refresh**
**💚 Error recovery and health monitoring built-in**

## 📋 Detailed Setup

### Prerequisites
- Python 3.8+
- Zerodha Kite account
- TOTP secret from Kite (for auto-login)

### Authentication Setup
1. **Get your Kite credentials:**
   - User ID
   - Password
   - TOTP Secret (from Kite app settings)

2. **Update `auto_token.py`:**
   ```python
   user_id = "YOUR_USER_ID"
   password = "YOUR_PASSWORD"
   totp_secret = "YOUR_TOTP_SECRET"
   ```

3. **Generate enctoken:**
   ```bash
   python auto_token.py
   ```

### Configuration
- **Symbols:** NIFTY, BANKNIFTY, FINNIFTY
- **Timeframes:** 1min, 5min, 15min
- **Refresh Rate:** 1-10 seconds (configurable)
- **Patterns:** 16 different chart patterns
- **Indicators:** MACD, RSI, VWAP, Bollinger Bands, Stochastic, ATR

## 🎛️ Dashboard Usage

### Left Panel - Controls & Market Info
- 📊 Symbol selection (NIFTY/BANKNIFTY/FINNIFTY)
- 🔄 Auto-refresh toggle and interval
- 🔌 Connection status
- 💰 Current price, high, low, volume
- 🔧 Technical indicators (RSI, MACD, VWAP)

### Center Panel - Live Chart
- 📈 Interactive candlestick chart
- 🧩 Pattern overlays and annotations
- 🔻 Support/resistance lines
- 📊 Volume bars
- 🔍 Zoom and pan controls

### Right Panel - Signals & Analysis
- 🎯 ML signal (Buy CE/PE) with confidence
- 🛑 Stop loss and target levels
- 📊 Trend analysis (Bullish/Bearish/Sideways)
- 🧩 Recent pattern list
- 📊 Key support/resistance levels

## 🧠 ML Model Details

### Features Used
- **MACD:** Trend momentum
- **RSI:** Overbought/oversold conditions
- **VWAP:** Volume-weighted average price
- **Pattern Context:** Recent chart patterns
- **Trend Direction:** Overall market direction

### Signal Enhancement
- Pattern-based confidence adjustment
- Trend alignment bonus/penalty
- Support/resistance level consideration
- ATR-based stop loss calculation

## 🧩 Pattern Detection

### Single Candle Patterns
- Doji, Hammer, Shooting Star, Spinning Top

### Two Candle Patterns
- Bullish/Bearish Engulfing
- Piercing Pattern, Dark Cloud Cover

### Complex Patterns
- Head & Shoulders (Regular & Inverse)
- Triangles (Ascending, Descending, Symmetrical)
- Flags (Bull Flag, Bear Flag)
- Double Top/Bottom

## 📊 Technical Indicators

### Trend Indicators
- **EMA 12/26:** Short/medium term trends
- **SMA 20/50:** Moving average support/resistance
- **MACD:** Momentum and trend changes

### Oscillators
- **RSI:** Relative strength (14-period)
- **Stochastic:** %K and %D oscillator
- **Bollinger Bands:** Volatility bands

### Volume & Volatility
- **VWAP:** Volume-weighted average price
- **ATR:** Average true range for volatility

## 🔧 Troubleshooting

### Common Issues

**1. WebSocket Connection Failed**
```bash
# Check enctoken validity
cat enctoken.txt

# Regenerate if expired
python auto_token.py
```

**2. Missing Dependencies**
```bash
# Install missing packages
pip install websocket-client plotly scipy
```

**3. Pattern Detection Errors**
```bash
# Test individual components
python test_system.py
```

**4. Chart Not Loading**
- Check internet connection
- Verify instrument token
- Restart dashboard

### Performance Tips
- Use 3-5 second refresh for optimal performance
- Limit chart history to 50-100 candles
- Close other browser tabs for better performance

## 📁 File Structure

```
📦 Tradebook2/ (Cleaned & Optimized)
├── 📄 live_dashboard.py      # Main live dashboard (auto-refresh enabled)
├── 📄 start.py               # Optimized startup script
├── 📄 kite_websocket.py      # WebSocket engine
├── 📄 kite_auth_helper.py    # Authentication helper
├── 📄 pattern_engine.py      # Pattern detection
├── 📄 auto_token.py          # Token generator
├── 📄 refresh_token.py       # Token refresh utility
├── 📄 train_with_real_data.py # Model training
├── 📁 ml_models/
│   ├── 📄 predictor.py       # ML prediction engine
│   ├── 📄 real_data_classifier.pkl  # Main trained model
│   ├── 📄 real_data_scaler.pkl      # Feature scaler
│   └── 📄 real_data_model_info.json # Model metadata
├── 📁 utils/
│   ├── 📄 indicators.py      # Technical indicators
│   └── 📄 chart_utils.py     # Chart visualization
└── 📁 data/
    └── 📄 training_data_90days.csv  # Real historical data
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## ⚠️ Disclaimer

This software is for educational purposes only. Trading involves risk and you should never trade with money you cannot afford to lose. The authors are not responsible for any financial losses.

## 📞 Support

For issues and questions:
- 🐛 Bug reports: Create GitHub issue
- 💡 Feature requests: Create GitHub issue
- 📧 General questions: Contact maintainers

---

**Happy Trading! 🚀📈**
