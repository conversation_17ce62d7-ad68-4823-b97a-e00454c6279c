import requests
import pandas as pd
from datetime import datetime

def get_kite_session():
    with open("enctoken.txt", "r") as f:
        enctoken = f.read().strip()

    session = requests.Session()
    session.headers.update({
        "Authorization": f"enctoken {enctoken}"
    })
    return session

def get_historical_data(symbol, interval="5minute", from_date=None, to_date=None):
    instrument_map = {
        "NIFTY": "NSE:NIFTY 50",
        "BANKNIFTY": "NSE:NIFTY BANK",
        "FINNIFTY": "NSE:NIFTY FIN SERVICE"
    }

    if symbol not in instrument_map:
        raise ValueError(f"Unknown symbol: {symbol}")

    # Convert datetime to string if needed
    from_date = pd.to_datetime(from_date).strftime("%Y-%m-%dT%H:%M:%S")
    to_date = pd.to_datetime(to_date).strftime("%Y-%m-%dT%H:%M:%S")

    session = get_kite_session()

    # Get instrument token
    ltp_url = f"https://api.kite.trade/ltp/{instrument_map[symbol]}"
    ltp_resp = session.get(ltp_url)
    instrument_token = ltp_resp.json()["data"][instrument_map[symbol]]["instrument_token"]

    # Historical data API
    url = (
        f"https://api.kite.trade/instruments/historical/{instrument_token}/"
        f"{interval}?from={from_date}&to={to_date}&continuous=false"
    )
    resp = session.get(url)

    if resp.status_code != 200:
        print("Kite API error:", resp.text)
        return pd.DataFrame()

    candles = resp.json()["data"]["candles"]
    df = pd.DataFrame(candles, columns=["timestamp", "open", "high", "low", "close", "volume"])
    df["datetime"] = pd.to_datetime(df["timestamp"])
    return df
