#!/usr/bin/env python3
"""
Test Timezone Fix for Real-time Updates
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_timezone_comparison():
    """Test timezone-aware vs timezone-naive timestamp comparisons"""
    print("🧪 Testing Timezone Comparison Fix")
    print("=" * 40)
    
    try:
        # Create timezone-aware data (like from Kite API)
        tz_aware_times = pd.date_range(
            start=datetime.now() - timedelta(hours=2),
            periods=10,
            freq='5min',
            tz='Asia/Kolkata'
        )
        
        # Create timezone-naive data (like from WebSocket)
        tz_naive_times = pd.date_range(
            start=datetime.now() - timedelta(hours=1),
            periods=5,
            freq='5min'
        )
        
        print(f"📊 Timezone-aware data: {len(tz_aware_times)} timestamps")
        print(f"   Sample: {tz_aware_times[0]} (tz: {tz_aware_times[0].tz})")
        
        print(f"📊 Timezone-naive data: {len(tz_naive_times)} timestamps")
        print(f"   Sample: {tz_naive_times[0]} (tz: {tz_naive_times[0].tz})")
        
        # Test direct comparison (should fail)
        print("\n🔍 Testing direct comparison...")
        try:
            result = tz_aware_times[0] > tz_naive_times[0]
            print(f"   Direct comparison result: {result}")
            direct_comparison_works = True
        except Exception as e:
            print(f"   ❌ Direct comparison failed: {e}")
            direct_comparison_works = False
        
        # Test timezone normalization (should work)
        print("\n🔍 Testing timezone normalization...")
        try:
            # Convert both to timezone-naive
            tz_aware_naive = tz_aware_times.tz_localize(None)
            tz_naive_naive = tz_naive_times
            
            # Now compare
            latest_historical = tz_aware_naive.max()
            newer_data = tz_naive_naive[tz_naive_naive > latest_historical]
            
            print(f"   ✅ Normalization successful")
            print(f"   Latest historical: {latest_historical}")
            print(f"   Newer data count: {len(newer_data)}")
            normalization_works = True
            
        except Exception as e:
            print(f"   ❌ Normalization failed: {e}")
            normalization_works = False
        
        return normalization_works
        
    except Exception as e:
        print(f"❌ Timezone test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_accumulation_with_timezones():
    """Test the actual data accumulation function with mixed timezones"""
    print("\n🧪 Testing Data Accumulation with Mixed Timezones")
    print("=" * 50)
    
    try:
        # Mock session state
        class MockSessionState:
            def __init__(self):
                self.todays_accumulated_data = pd.DataFrame()
        
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
        
        # Import the function
        from live_dashboard import accumulate_todays_data
        
        # Create initial data with timezone
        initial_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
        initial_data = pd.DataFrame({
            'timestamp': pd.date_range(start=initial_time, periods=3, freq='5min', tz='Asia/Kolkata'),
            'open': [22000, 22010, 22020],
            'high': [22005, 22015, 22025],
            'low': [21995, 22005, 22015],
            'close': [22010, 22020, 22030],
            'volume': [10000, 12000, 11000]
        })
        
        print(f"📊 Initial data: {len(initial_data)} candles with timezone")
        print(f"   Timezone: {initial_data['timestamp'].iloc[0].tz}")
        
        # Accumulate initial data
        result1 = accumulate_todays_data(initial_data, "NIFTY", "5minute")
        print(f"✅ First accumulation: {len(result1)} candles")
        
        # Create new data without timezone
        new_time = initial_time + timedelta(minutes=15)
        new_data = pd.DataFrame({
            'timestamp': pd.date_range(start=new_time, periods=2, freq='5min'),  # No timezone
            'open': [22030, 22040],
            'high': [22035, 22045],
            'low': [22025, 22035],
            'close': [22040, 22050],
            'volume': [13000, 14000]
        })
        
        print(f"📊 New data: {len(new_data)} candles without timezone")
        print(f"   Timezone: {new_data['timestamp'].iloc[0].tz}")
        
        # Accumulate new data
        result2 = accumulate_todays_data(new_data, "NIFTY", "5minute")
        print(f"✅ Second accumulation: {len(result2)} candles")
        
        # Test live update (same timestamp)
        live_update = pd.DataFrame({
            'timestamp': [new_time + timedelta(minutes=5)],  # Same as last candle
            'open': [22040],
            'high': [22048],  # Updated
            'low': [22035],
            'close': [22045],  # Updated
            'volume': [15000]  # Updated
        })
        
        print(f"📊 Live update: {len(live_update)} candle")
        
        # Accumulate live update
        result3 = accumulate_todays_data(live_update, "NIFTY", "5minute")
        print(f"✅ Live update accumulation: {len(result3)} candles")
        
        # Verify final result
        if len(result3) == 5:  # 3 + 2 = 5 candles
            print("✅ Mixed timezone accumulation working correctly")
            return True
        else:
            print(f"⚠️ Expected 5 candles, got {len(result3)}")
            return False
            
    except Exception as e:
        print(f"❌ Data accumulation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_seamless_data_combination():
    """Test seamless data combination with timezone fixes"""
    print("\n🧪 Testing Seamless Data Combination with Timezone Fix")
    print("=" * 55)
    
    try:
        # Create historical data with timezone
        historical_data = pd.DataFrame({
            'timestamp': pd.date_range(
                start=datetime.now() - timedelta(days=1, hours=2),
                periods=50,
                freq='5min',
                tz='Asia/Kolkata'
            ),
            'open': np.random.uniform(22000, 22100, 50),
            'high': np.random.uniform(22050, 22150, 50),
            'low': np.random.uniform(21950, 22050, 50),
            'close': np.random.uniform(22000, 22100, 50),
            'volume': np.random.randint(10000, 50000, 50)
        })
        
        # Create today's data without timezone
        todays_data = pd.DataFrame({
            'timestamp': pd.date_range(
                start=datetime.now() - timedelta(hours=1),
                periods=10,
                freq='5min'
            ),
            'open': np.random.uniform(22100, 22200, 10),
            'high': np.random.uniform(22150, 22250, 10),
            'low': np.random.uniform(22050, 22150, 10),
            'close': np.random.uniform(22100, 22200, 10),
            'volume': np.random.randint(15000, 40000, 10)
        })
        
        print(f"📊 Historical data: {len(historical_data)} candles (with timezone)")
        print(f"📊 Today's data: {len(todays_data)} candles (without timezone)")
        
        # Test the fixed combination logic
        # Ensure both timestamps are timezone-naive for comparison
        historical_timestamps = historical_data['timestamp'].copy()
        todays_timestamps = todays_data['timestamp'].copy()
        
        # Convert to timezone-naive if needed
        if historical_timestamps.dt.tz is not None:
            historical_timestamps = historical_timestamps.dt.tz_localize(None)
        if todays_timestamps.dt.tz is not None:
            todays_timestamps = todays_timestamps.dt.tz_localize(None)
        
        # Find cutoff point to avoid overlap
        historical_end_time = historical_timestamps.max()
        todays_new_data = todays_data[todays_timestamps > historical_end_time]
        
        print(f"✅ Timezone normalization successful")
        print(f"   Historical end time: {historical_end_time}")
        print(f"   Today's new data: {len(todays_new_data)} candles")
        
        if not todays_new_data.empty:
            # Combine historical + today's new data
            combined_candles = pd.concat([
                historical_data.tail(50),  # Last 50 historical candles
                todays_new_data
            ], ignore_index=True)
            
            print(f"✅ Combined data: {len(combined_candles)} candles")
            print(f"   Historical: {len(historical_data.tail(50))} candles")
            print(f"   Today's new: {len(todays_new_data)} candles")
            
            return True
        else:
            print("⚠️ No new today's data to combine (expected for test)")
            return True
            
    except Exception as e:
        print(f"❌ Seamless combination test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 TESTING TIMEZONE FIX FOR REAL-TIME UPDATES")
    print("=" * 60)
    
    # Run all tests
    test_results = {
        'Timezone Comparison': test_timezone_comparison(),
        'Data Accumulation': test_data_accumulation_with_timezones(),
        'Seamless Combination': test_seamless_data_combination()
    }
    
    # Summary
    print(f"\n📊 TIMEZONE FIX TEST SUMMARY:")
    print("=" * 35)
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    all_passed = all(test_results.values())
    if all_passed:
        print(f"\n🎉 ALL TIMEZONE TESTS PASSED!")
        print("   The real-time update timezone issue should be fixed.")
        print("   Mixed timezone data can now be compared safely.")
        print("   Dashboard real-time updates should work correctly.")
    else:
        print(f"\n⚠️ SOME TIMEZONE TESTS FAILED!")
        print("   Check the error messages above to identify remaining issues.")
        failed_tests = [name for name, result in test_results.items() if not result]
        print(f"   Failed tests: {failed_tests}")

if __name__ == "__main__":
    main()
