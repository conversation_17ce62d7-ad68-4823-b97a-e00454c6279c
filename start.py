#!/usr/bin/env python3
"""
Simple startup script for the Live Options Trading Dashboard
Optimized for auto-refresh and live data
"""

import subprocess
import sys
import os

def main():
    """Start the dashboard with optimized settings"""
    print("🚀 Starting Live Options Trading Dashboard...")
    print("📊 Auto-refresh enabled by default")
    print("🔄 WebSocket-only updates for stability")
    print("💡 Access at: http://localhost:8502")
    print("-" * 50)
    
    try:
        # Start Streamlit with optimized settings
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "live_dashboard.py",
            "--server.port=8502",
            "--server.headless=true",
            "--browser.gatherUsageStats=false"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
