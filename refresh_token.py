import os
import sys
import importlib.util
import logging
import requests
import pyotp
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_credentials_from_auto_token():
    """Load credentials from auto_token.py"""
    try:
        # Import auto_token.py as a module
        spec = importlib.util.spec_from_file_location("auto_token", "auto_token.py")
        auto_token = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(auto_token)
        
        # Extract credentials
        user_id = getattr(auto_token, "user_id", None)
        password = getattr(auto_token, "password", None)
        totp_secret = getattr(auto_token, "totp_secret", None)
        
        if not user_id or not password:
            logger.error("Missing credentials in auto_token.py")
            return None, None, None
            
        return user_id, password, totp_secret
    except Exception as e:
        logger.error(f"Error loading credentials from auto_token.py: {e}")
        return None, None, None

def get_enctoken(user_id, password, totp_secret):
    """
    Get enctoken from Kit<PERSON> using requests
    
    Args:
        user_id (str): Zerodha user ID
        password (str): Zerodha password
        totp_secret (str): TOTP secret key for 2FA
        
    Returns:
        str: enctoken if successful, None otherwise
    """
    try:
        # Create a session
        session = requests.Session()
        
        # Step 1: Login with user ID and password
        logger.info(f"Logging in with user ID: {user_id}")
        login_url = "https://kite.zerodha.com/api/login"
        login_data = {
            "user_id": user_id,
            "password": password
        }
        
        login_resp = session.post(login_url, data=login_data)
        
        if login_resp.status_code != 200:
            logger.error(f"Login failed: {login_resp.text}")
            return None
        
        login_data = login_resp.json()
        if login_data.get("status") != "success":
            logger.error(f"Login failed: {login_data.get('message')}")
            return None
        
        # Extract request_id for 2FA
        request_id = login_data.get("data", {}).get("request_id")
        if not request_id:
            logger.error("Failed to get request_id for 2FA")
            return None
        
        # Step 2: Submit TOTP for 2FA
        logger.info("Generating TOTP for 2FA")
        totp = pyotp.TOTP(totp_secret)
        totp_value = totp.now()
        logger.info(f"Generated TOTP: {totp_value}")
        
        twofa_url = "https://kite.zerodha.com/api/twofa"
        twofa_data = {
            "user_id": user_id,
            "request_id": request_id,
            "twofa_value": totp_value
        }
        
        twofa_resp = session.post(twofa_url, data=twofa_data)
        
        if twofa_resp.status_code != 200:
            logger.error(f"2FA failed: {twofa_resp.text}")
            return None
        
        # Check for enctoken in cookies
        if "enctoken" in session.cookies:
            enctoken = session.cookies["enctoken"]
            logger.info("Successfully retrieved enctoken from cookies")
            return enctoken
        
        # If not in cookies, try to get from response
        twofa_data = twofa_resp.json()
        if twofa_data.get("status") != "success":
            logger.error(f"2FA failed: {twofa_data.get('message')}")
            return None
        
        logger.error("Enctoken not found in cookies or response")
        return None
            
    except Exception as e:
        logger.error(f"Error getting enctoken: {e}")
        return None

def main():
    # Load credentials from auto_token.py
    user_id, password, totp_secret = load_credentials_from_auto_token()
    
    if not user_id or not password or not totp_secret:
        logger.error("Failed to load credentials from auto_token.py")
        sys.exit(1)
    
    # Get enctoken
    enctoken = get_enctoken(user_id, password, totp_secret)
    
    if enctoken:
        # Save enctoken to file
        with open("enctoken.txt", "w") as f:
            f.write(enctoken)
        logger.info("Enctoken saved to enctoken.txt")
        
        # Print enctoken
        print(f"\nEnctoken: {enctoken[:20]}...\n")
        print("This token will expire in 24 hours. Run this script again to generate a new token.")
    else:
        logger.error("Failed to generate enctoken")
        sys.exit(1)

if __name__ == "__main__":
    main()
