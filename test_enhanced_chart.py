#!/usr/bin/env python3
"""
Test Enhanced Zerodha-style Chart with ML Predictions
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """Create realistic test data for chart testing"""
    
    # Generate 200 candles of realistic NIFTY data
    np.random.seed(42)
    
    # Base price around NIFTY levels
    base_price = 22000
    
    # Generate realistic price movements
    returns = np.random.normal(0, 0.008, 200)  # 0.8% volatility
    prices = base_price * np.exp(np.cumsum(returns))
    
    # Generate timestamps (5-minute intervals)
    start_time = datetime.now() - timedelta(minutes=200*5)
    timestamps = [start_time + timedelta(minutes=i*5) for i in range(200)]
    
    # Generate OHLC data
    high_prices = prices * (1 + np.random.uniform(0, 0.003, 200))
    low_prices = prices * (1 - np.random.uniform(0, 0.003, 200))
    open_prices = np.roll(prices, 1)
    open_prices[0] = prices[0]
    
    # Generate volume
    volumes = np.random.randint(50000, 200000, 200)
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': prices,
        'volume': volumes
    })
    
    return df

def create_mock_ml_prediction():
    """Create a mock ML prediction for testing"""
    
    return {
        'signal': 'Buy CE',
        'confidence': 78.5,
        'stoploss': 21850,
        'target': 22250,
        'current_price': 22000,
        'trend': 'Bullish',
        'risk_reward_ratio': '1:2.5'
    }

def test_zerodha_chart():
    """Test the new Zerodha-style chart"""
    
    print("🧪 Testing Enhanced Zerodha-style Chart")
    print("=" * 50)
    
    try:
        # Import required modules
        from utils.chart_utils import create_zerodha_style_chart
        from utils.indicators import calculate_indicators
        from pattern_engine import detect_patterns, calculate_support_resistance
        
        # Create test data
        print("📊 Creating test data...")
        df = create_test_data()
        
        # Calculate indicators
        print("📈 Calculating indicators...")
        df = calculate_indicators(df)
        
        # Detect patterns and levels
        print("🔍 Detecting patterns...")
        patterns = detect_patterns(df)
        support_levels, resistance_levels = calculate_support_resistance(df)
        
        # Create mock ML prediction
        print("🤖 Creating ML prediction...")
        ml_prediction = create_mock_ml_prediction()
        
        # Create the chart
        print("📈 Creating Zerodha-style chart...")
        fig = create_zerodha_style_chart(
            df.tail(100),  # Last 100 candles
            patterns=patterns,
            support_levels=support_levels,
            resistance_levels=resistance_levels,
            ml_prediction=ml_prediction,
            show_indicators=True
        )
        
        print("✅ Chart created successfully!")
        print(f"   • Data points: {len(df)}")
        print(f"   • Patterns detected: {len(patterns)}")
        print(f"   • Support levels: {len(support_levels)}")
        print(f"   • Resistance levels: {len(resistance_levels)}")
        print(f"   • ML Signal: {ml_prediction['signal']}")
        print(f"   • Confidence: {ml_prediction['confidence']:.1f}%")
        
        # Save chart as HTML for testing
        print("💾 Saving chart as HTML...")
        fig.write_html("test_zerodha_chart.html")
        print("✅ Chart saved as 'test_zerodha_chart.html'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chart: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_professional_chart():
    """Test the enhanced professional chart with ML predictions"""
    
    print("\n🧪 Testing Enhanced Professional Chart")
    print("=" * 50)
    
    try:
        # Import required modules
        from utils.chart_utils import create_professional_candlestick_chart
        from utils.indicators import calculate_indicators
        from pattern_engine import detect_patterns, calculate_support_resistance
        
        # Create test data
        print("📊 Creating test data...")
        df = create_test_data()
        
        # Calculate indicators
        print("📈 Calculating indicators...")
        df = calculate_indicators(df)
        
        # Detect patterns and levels
        print("🔍 Detecting patterns...")
        patterns = detect_patterns(df)
        support_levels, resistance_levels = calculate_support_resistance(df)
        
        # Create mock ML prediction
        print("🤖 Creating ML prediction...")
        ml_prediction = create_mock_ml_prediction()
        
        # Create the chart
        print("📈 Creating professional chart...")
        fig = create_professional_candlestick_chart(
            df.tail(100),  # Last 100 candles
            patterns=patterns,
            support_levels=support_levels,
            resistance_levels=resistance_levels,
            selected_indicators=['VWAP', 'RSI'],
            ml_prediction=ml_prediction
        )
        
        print("✅ Professional chart created successfully!")
        
        # Save chart as HTML for testing
        print("💾 Saving chart as HTML...")
        fig.write_html("test_professional_chart.html")
        print("✅ Chart saved as 'test_professional_chart.html'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing professional chart: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🚀 TESTING ENHANCED CHART FUNCTIONALITY")
    print("=" * 60)
    
    # Test Zerodha-style chart
    zerodha_success = test_zerodha_chart()
    
    # Test professional chart
    professional_success = test_professional_chart()
    
    # Summary
    print(f"\n📊 TEST SUMMARY:")
    print(f"   • Zerodha Chart: {'✅ PASS' if zerodha_success else '❌ FAIL'}")
    print(f"   • Professional Chart: {'✅ PASS' if professional_success else '❌ FAIL'}")
    
    if zerodha_success and professional_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   • Open 'test_zerodha_chart.html' to see Zerodha-style chart")
        print(f"   • Open 'test_professional_chart.html' to see professional chart")
        print(f"   • Both charts include ML predictions and real-time styling")
    else:
        print(f"\n⚠️ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
