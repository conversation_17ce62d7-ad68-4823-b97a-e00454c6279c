import pandas as pd
import numpy as np
from scipy.signal import find_peaks

def detect_patterns(df):
    """Detect all 16 chart patterns and support/resistance levels"""
    patterns = []

    # Ensure we have enough data
    if len(df) < 20:
        return patterns

    # Detect candlestick patterns
    for i in range(10, len(df)):
        window = df.iloc[i-10:i+1]

        # Single candle patterns
        if is_doji(window):
            patterns.append((i, "Doji"))
        elif is_hammer(window):
            patterns.append((i, "Hammer"))
        elif is_shooting_star(window):
            patterns.append((i, "Shooting Star"))
        elif is_spinning_top(window):
            patterns.append((i, "Spinning Top"))

        # Two candle patterns
        if i >= 11:
            if is_bullish_engulfing(window):
                patterns.append((i, "Bullish Engulfing"))
            elif is_bearish_engulfing(window):
                patterns.append((i, "Bearish Engulfing"))
            elif is_piercing_pattern(window):
                patterns.append((i, "Piercing Pattern"))
            elif is_dark_cloud_cover(window):
                patterns.append((i, "Dark Cloud Cover"))

        # Multi-candle patterns (need more data)
        if i >= 15:
            if is_head_and_shoulders(window):
                patterns.append((i, "Head and Shoulders"))
            elif is_inverse_head_and_shoulders(window):
                patterns.append((i, "Inverse Head and Shoulders"))
            elif is_ascending_triangle(window):
                patterns.append((i, "Ascending Triangle"))
            elif is_descending_triangle(window):
                patterns.append((i, "Descending Triangle"))
            elif is_symmetrical_triangle(window):
                patterns.append((i, "Symmetrical Triangle"))
            elif is_bull_flag(window):
                patterns.append((i, "Bull Flag"))
            elif is_bear_flag(window):
                patterns.append((i, "Bear Flag"))
            elif is_double_top(window):
                patterns.append((i, "Double Top"))
            elif is_double_bottom(window):
                patterns.append((i, "Double Bottom"))

    return patterns

def calculate_support_resistance(df, lookback=20):
    """Calculate dynamic support and resistance levels"""
    if len(df) < lookback:
        return [], []

    # Get recent data
    recent_data = df.tail(lookback * 2)
    highs = recent_data['high'].values
    lows = recent_data['low'].values

    # Find peaks and troughs
    resistance_peaks, _ = find_peaks(highs, distance=5, prominence=np.std(highs) * 0.5)
    support_peaks, _ = find_peaks(-lows, distance=5, prominence=np.std(lows) * 0.5)

    # Get resistance levels (peaks in highs)
    resistance_levels = []
    if len(resistance_peaks) > 0:
        resistance_values = highs[resistance_peaks]
        # Group similar levels
        for level in resistance_values:
            if not any(abs(level - existing) < level * 0.002 for existing in resistance_levels):
                resistance_levels.append(level)

    # Get support levels (troughs in lows)
    support_levels = []
    if len(support_peaks) > 0:
        support_values = lows[support_peaks]
        # Group similar levels
        for level in support_values:
            if not any(abs(level - existing) < level * 0.002 for existing in support_levels):
                support_levels.append(level)

    # Sort and return top 3 of each
    resistance_levels = sorted(resistance_levels, reverse=True)[:3]
    support_levels = sorted(support_levels, reverse=True)[:3]

    return support_levels, resistance_levels

# ============ CANDLESTICK PATTERN FUNCTIONS ============

def is_bullish_engulfing(window):
    """Bullish engulfing pattern"""
    if len(window) < 2:
        return False
    return (window.iloc[-2]['close'] < window.iloc[-2]['open'] and
            window.iloc[-1]['close'] > window.iloc[-1]['open'] and
            window.iloc[-1]['close'] > window.iloc[-2]['open'] and
            window.iloc[-1]['open'] < window.iloc[-2]['close'])

def is_bearish_engulfing(window):
    """Bearish engulfing pattern"""
    if len(window) < 2:
        return False
    return (window.iloc[-2]['close'] > window.iloc[-2]['open'] and
            window.iloc[-1]['close'] < window.iloc[-1]['open'] and
            window.iloc[-1]['close'] < window.iloc[-2]['open'] and
            window.iloc[-1]['open'] > window.iloc[-2]['close'])

def is_doji(window):
    """Doji candlestick pattern"""
    candle = window.iloc[-1]
    body_size = abs(candle['close'] - candle['open'])
    total_range = candle['high'] - candle['low']
    return total_range > 0 and body_size / total_range < 0.1

def is_hammer(window):
    """Hammer candlestick pattern"""
    candle = window.iloc[-1]
    body = abs(candle['close'] - candle['open'])
    lower_wick = min(candle['close'], candle['open']) - candle['low']
    upper_wick = candle['high'] - max(candle['close'], candle['open'])
    total_range = candle['high'] - candle['low']

    if total_range == 0:
        return False
    return (lower_wick > 2 * body and
            upper_wick < body * 0.5 and
            body / total_range < 0.3)

def is_shooting_star(window):
    """Shooting star candlestick pattern"""
    candle = window.iloc[-1]
    body = abs(candle['close'] - candle['open'])
    upper_wick = candle['high'] - max(candle['close'], candle['open'])
    lower_wick = min(candle['close'], candle['open']) - candle['low']
    total_range = candle['high'] - candle['low']

    if total_range == 0:
        return False
    return (upper_wick > 2 * body and
            lower_wick < body * 0.5 and
            body / total_range < 0.3)

def is_spinning_top(window):
    """Spinning top candlestick pattern"""
    candle = window.iloc[-1]
    body = abs(candle['close'] - candle['open'])
    upper_wick = candle['high'] - max(candle['close'], candle['open'])
    lower_wick = min(candle['close'], candle['open']) - candle['low']
    total_range = candle['high'] - candle['low']

    if total_range == 0:
        return False
    return (body / total_range < 0.3 and
            abs(upper_wick - lower_wick) / total_range < 0.2)

def is_piercing_pattern(window):
    """Piercing pattern (bullish reversal)"""
    if len(window) < 2:
        return False
    prev_candle = window.iloc[-2]
    curr_candle = window.iloc[-1]

    # Previous candle should be bearish
    if prev_candle['close'] >= prev_candle['open']:
        return False

    # Current candle should be bullish
    if curr_candle['close'] <= curr_candle['open']:
        return False

    # Current open should be below previous low
    if curr_candle['open'] >= prev_candle['low']:
        return False

    # Current close should be above midpoint of previous candle
    prev_midpoint = (prev_candle['open'] + prev_candle['close']) / 2
    return curr_candle['close'] > prev_midpoint

def is_dark_cloud_cover(window):
    """Dark cloud cover pattern (bearish reversal)"""
    if len(window) < 2:
        return False
    prev_candle = window.iloc[-2]
    curr_candle = window.iloc[-1]

    # Previous candle should be bullish
    if prev_candle['close'] <= prev_candle['open']:
        return False

    # Current candle should be bearish
    if curr_candle['close'] >= curr_candle['open']:
        return False

    # Current open should be above previous high
    if curr_candle['open'] <= prev_candle['high']:
        return False

    # Current close should be below midpoint of previous candle
    prev_midpoint = (prev_candle['open'] + prev_candle['close']) / 2
    return curr_candle['close'] < prev_midpoint

# ============ COMPLEX CHART PATTERNS ============

def is_head_and_shoulders(window):
    """Head and shoulders pattern"""
    if len(window) < 15:
        return False

    highs = window['high'].values
    peaks, _ = find_peaks(highs, distance=3)

    if len(peaks) < 3:
        return False

    # Get the last 3 peaks
    last_peaks = peaks[-3:]
    peak_values = highs[last_peaks]

    # Check if middle peak is highest (head) and side peaks are similar (shoulders)
    left_shoulder, head, right_shoulder = peak_values

    return (head > left_shoulder and head > right_shoulder and
            abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05)

def is_inverse_head_and_shoulders(window):
    """Inverse head and shoulders pattern"""
    if len(window) < 15:
        return False

    lows = window['low'].values
    troughs, _ = find_peaks(-lows, distance=3)

    if len(troughs) < 3:
        return False

    # Get the last 3 troughs
    last_troughs = troughs[-3:]
    trough_values = lows[last_troughs]

    # Check if middle trough is lowest (head) and side troughs are similar (shoulders)
    left_shoulder, head, right_shoulder = trough_values

    return (head < left_shoulder and head < right_shoulder and
            abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05)

def is_ascending_triangle(window):
    """Ascending triangle pattern"""
    if len(window) < 10:
        return False

    highs = window['high'].values
    lows = window['low'].values

    # Check if highs are relatively flat (resistance)
    high_slope = np.polyfit(range(len(highs)), highs, 1)[0]

    # Check if lows are ascending (support)
    low_slope = np.polyfit(range(len(lows)), lows, 1)[0]

    return abs(high_slope) < 0.1 and low_slope > 0.1

def is_descending_triangle(window):
    """Descending triangle pattern"""
    if len(window) < 10:
        return False

    highs = window['high'].values
    lows = window['low'].values

    # Check if lows are relatively flat (support)
    low_slope = np.polyfit(range(len(lows)), lows, 1)[0]

    # Check if highs are descending (resistance)
    high_slope = np.polyfit(range(len(highs)), highs, 1)[0]

    return abs(low_slope) < 0.1 and high_slope < -0.1

def is_symmetrical_triangle(window):
    """Symmetrical triangle pattern"""
    if len(window) < 10:
        return False

    highs = window['high'].values
    lows = window['low'].values

    # Check if highs are descending and lows are ascending
    high_slope = np.polyfit(range(len(highs)), highs, 1)[0]
    low_slope = np.polyfit(range(len(lows)), lows, 1)[0]

    return high_slope < -0.05 and low_slope > 0.05

def is_bull_flag(window):
    """Bull flag pattern"""
    if len(window) < 10:
        return False

    # Check for strong upward move followed by consolidation
    first_half = window.iloc[:len(window)//2]
    second_half = window.iloc[len(window)//2:]

    # First half should show strong upward movement
    first_slope = np.polyfit(range(len(first_half)), first_half['close'].values, 1)[0]

    # Second half should show slight downward consolidation
    second_slope = np.polyfit(range(len(second_half)), second_half['close'].values, 1)[0]

    return first_slope > 0.5 and -0.2 < second_slope < 0.1

def is_bear_flag(window):
    """Bear flag pattern"""
    if len(window) < 10:
        return False

    # Check for strong downward move followed by consolidation
    first_half = window.iloc[:len(window)//2]
    second_half = window.iloc[len(window)//2:]

    # First half should show strong downward movement
    first_slope = np.polyfit(range(len(first_half)), first_half['close'].values, 1)[0]

    # Second half should show slight upward consolidation
    second_slope = np.polyfit(range(len(second_half)), second_half['close'].values, 1)[0]

    return first_slope < -0.5 and -0.1 < second_slope < 0.2

def is_double_top(window):
    """Double top pattern"""
    if len(window) < 15:
        return False

    highs = window['high'].values
    peaks, _ = find_peaks(highs, distance=5)

    if len(peaks) < 2:
        return False

    # Get the last 2 peaks
    last_peaks = peaks[-2:]
    peak_values = highs[last_peaks]

    # Check if peaks are at similar levels
    return abs(peak_values[0] - peak_values[1]) / max(peak_values) < 0.03

def is_double_bottom(window):
    """Double bottom pattern"""
    if len(window) < 15:
        return False

    lows = window['low'].values
    troughs, _ = find_peaks(-lows, distance=5)

    if len(troughs) < 2:
        return False

    # Get the last 2 troughs
    last_troughs = troughs[-2:]
    trough_values = lows[last_troughs]

    # Check if troughs are at similar levels
    return abs(trough_values[0] - trough_values[1]) / max(trough_values) < 0.03