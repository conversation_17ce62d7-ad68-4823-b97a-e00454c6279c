#!/usr/bin/env python3
"""
Test Chart Fallback Mechanisms
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_fallbacks():
    """Test all chart creation fallback mechanisms"""
    print("🧪 Testing Chart Fallback Mechanisms")
    print("=" * 40)
    
    try:
        # Create test data
        print("📊 Creating test data...")
        dates = pd.date_range(start=datetime.now() - timedelta(hours=5), periods=50, freq='5min')
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(22000, 22100, 50),
            'high': np.random.uniform(22050, 22150, 50),
            'low': np.random.uniform(21950, 22050, 50),
            'close': np.random.uniform(22000, 22100, 50),
            'volume': np.random.randint(10000, 50000, 50)
        })
        
        # Ensure high >= low, open/close within range
        test_data['high'] = np.maximum(test_data['high'], test_data[['open', 'close']].max(axis=1))
        test_data['low'] = np.minimum(test_data['low'], test_data[['open', 'close']].min(axis=1))
        
        print(f"✅ Created test data: {len(test_data)} candles")
        
        # Test 1: Full Zerodha chart
        print("\n📈 Testing Full Zerodha Chart...")
        try:
            from utils.chart_utils import create_zerodha_style_chart
            from pattern_engine import detect_patterns, calculate_support_resistance
            from ml_models.predictor import predict_ce_pe
            
            # Get patterns and ML prediction
            patterns = detect_patterns(test_data)
            support_levels, resistance_levels = calculate_support_resistance(test_data)
            ml_prediction = predict_ce_pe(test_data)
            
            fig = create_zerodha_style_chart(
                test_data,
                patterns=patterns,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                ml_prediction=ml_prediction,
                show_indicators=True
            )
            
            if fig is not None:
                fig.write_html("test_full_zerodha_chart.html")
                print("✅ Full Zerodha chart created successfully")
                print("💾 Saved as 'test_full_zerodha_chart.html'")
                full_chart_success = True
            else:
                print("❌ Full Zerodha chart returned None")
                full_chart_success = False
                
        except Exception as e:
            print(f"❌ Full Zerodha chart failed: {e}")
            full_chart_success = False
        
        # Test 2: Minimal Zerodha chart
        print("\n📈 Testing Minimal Zerodha Chart...")
        try:
            fig = create_zerodha_style_chart(
                test_data,
                patterns=[],
                support_levels=[],
                resistance_levels=[],
                ml_prediction=None,
                show_indicators=False
            )
            
            if fig is not None:
                fig.write_html("test_minimal_zerodha_chart.html")
                print("✅ Minimal Zerodha chart created successfully")
                print("💾 Saved as 'test_minimal_zerodha_chart.html'")
                minimal_chart_success = True
            else:
                print("❌ Minimal Zerodha chart returned None")
                minimal_chart_success = False
                
        except Exception as e:
            print(f"❌ Minimal Zerodha chart failed: {e}")
            minimal_chart_success = False
        
        # Test 3: Simple candlestick chart
        print("\n📈 Testing Simple Candlestick Chart...")
        try:
            from utils.chart_utils import create_simple_candlestick_chart
            
            fig = create_simple_candlestick_chart(test_data)
            
            if fig is not None:
                fig.write_html("test_simple_candlestick_chart.html")
                print("✅ Simple candlestick chart created successfully")
                print("💾 Saved as 'test_simple_candlestick_chart.html'")
                simple_chart_success = True
            else:
                print("❌ Simple candlestick chart returned None")
                simple_chart_success = False
                
        except Exception as e:
            print(f"❌ Simple candlestick chart failed: {e}")
            simple_chart_success = False
        
        # Test 4: Placeholder chart
        print("\n📈 Testing Placeholder Chart...")
        try:
            from utils.chart_utils import create_live_chart_placeholder
            
            fig = create_live_chart_placeholder()
            
            if fig is not None:
                fig.write_html("test_placeholder_chart.html")
                print("✅ Placeholder chart created successfully")
                print("💾 Saved as 'test_placeholder_chart.html'")
                placeholder_success = True
            else:
                print("❌ Placeholder chart returned None")
                placeholder_success = False
                
        except Exception as e:
            print(f"❌ Placeholder chart failed: {e}")
            placeholder_success = False
        
        # Summary
        results = {
            'Full Zerodha Chart': full_chart_success,
            'Minimal Zerodha Chart': minimal_chart_success,
            'Simple Candlestick Chart': simple_chart_success,
            'Placeholder Chart': placeholder_success
        }
        
        print(f"\n📊 FALLBACK TEST SUMMARY:")
        print("=" * 30)
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name}: {status}")
        
        # Check if at least one fallback works
        working_fallbacks = sum(results.values())
        if working_fallbacks > 0:
            print(f"\n🎉 SUCCESS: {working_fallbacks}/4 chart types working!")
            print("   The dashboard should be able to display charts.")
            if working_fallbacks < 4:
                print("   Some fallbacks failed, but others are available.")
            return True
        else:
            print(f"\n❌ FAILURE: No chart types working!")
            print("   The dashboard will not be able to display charts.")
            return False
            
    except Exception as e:
        print(f"❌ Chart fallback test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_validation():
    """Test data validation for chart creation"""
    print("\n🧪 Testing Data Validation")
    print("=" * 30)
    
    test_cases = [
        ("Empty DataFrame", pd.DataFrame()),
        ("Missing columns", pd.DataFrame({'timestamp': [datetime.now()], 'close': [100]})),
        ("NaN values", pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=3, freq='5min'),
            'open': [100, np.nan, 102],
            'high': [101, 103, np.nan],
            'low': [99, 101, 100],
            'close': [100.5, 102, 101],
            'volume': [1000, 2000, 1500]
        })),
        ("Valid data", pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=10, freq='5min'),
            'open': np.random.uniform(100, 110, 10),
            'high': np.random.uniform(105, 115, 10),
            'low': np.random.uniform(95, 105, 10),
            'close': np.random.uniform(100, 110, 10),
            'volume': np.random.randint(1000, 5000, 10)
        }))
    ]
    
    results = {}
    
    for test_name, test_data in test_cases:
        print(f"\n📊 Testing: {test_name}")
        try:
            from utils.chart_utils import create_simple_candlestick_chart
            
            if test_data.empty:
                print("   ⚠️ Empty data - should handle gracefully")
                fig = create_simple_candlestick_chart(test_data)
                results[test_name] = fig is not None
            else:
                # Fix high/low relationships for valid test
                if 'high' in test_data.columns and 'low' in test_data.columns:
                    test_data['high'] = np.maximum(test_data['high'], test_data[['open', 'close']].max(axis=1))
                    test_data['low'] = np.minimum(test_data['low'], test_data[['open', 'close']].min(axis=1))
                
                fig = create_simple_candlestick_chart(test_data)
                results[test_name] = fig is not None
                
            status = "✅ PASS" if results[test_name] else "❌ FAIL"
            print(f"   Result: {status}")
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            results[test_name] = False
    
    return results

def main():
    """Main test function"""
    print("🚀 TESTING CHART FALLBACK MECHANISMS")
    print("=" * 60)
    
    # Test chart fallbacks
    fallback_success = test_chart_fallbacks()
    
    # Test data validation
    validation_results = test_data_validation()
    
    print(f"\n📊 OVERALL SUMMARY:")
    print("=" * 30)
    print(f"   Chart Fallbacks: {'✅ WORKING' if fallback_success else '❌ FAILED'}")
    
    validation_success = sum(validation_results.values()) >= 1
    print(f"   Data Validation: {'✅ WORKING' if validation_success else '❌ FAILED'}")
    
    if fallback_success and validation_success:
        print(f"\n🎉 CHART SYSTEM IS ROBUST!")
        print("   Multiple fallback mechanisms are working.")
        print("   The dashboard should handle chart loading gracefully.")
        print("   Check the generated HTML files to verify chart rendering.")
    else:
        print(f"\n⚠️ CHART SYSTEM NEEDS ATTENTION!")
        if not fallback_success:
            print("   Chart creation mechanisms are failing.")
        if not validation_success:
            print("   Data validation is not working properly.")

if __name__ == "__main__":
    main()
