#!/usr/bin/env python3
"""
Diagnose Chart Loading Issues
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_format():
    """Test if data format is correct for chart creation"""
    print("🧪 Testing Data Format")
    print("=" * 30)
    
    try:
        # Import data function
        from live_dashboard import get_simulated_data
        
        # Get test data
        print("📊 Getting test data...")
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is None or candles.empty:
            print("❌ No data returned")
            return False
        
        print(f"✅ Got {len(candles)} candles")
        print(f"   Columns: {list(candles.columns)}")
        
        # Check required columns
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in candles.columns]
        
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return False
        
        print("✅ All required columns present")
        
        # Check data types
        print("📊 Data types:")
        for col in required_cols:
            dtype = candles[col].dtype
            print(f"   {col}: {dtype}")
            
            # Check for NaN values
            nan_count = candles[col].isna().sum()
            if nan_count > 0:
                print(f"   ⚠️ {col} has {nan_count} NaN values")
        
        # Check timestamp format
        if 'timestamp' in candles.columns:
            ts_sample = candles['timestamp'].iloc[0]
            print(f"   Timestamp sample: {ts_sample} (type: {type(ts_sample)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Data format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_dependencies():
    """Test if chart dependencies are available"""
    print("\n🧪 Testing Chart Dependencies")
    print("=" * 35)
    
    dependencies = {
        'plotly': 'plotly.graph_objects',
        'plotly.subplots': 'plotly.subplots',
        'mplfinance': 'mplfinance'
    }
    
    missing_deps = []
    
    for dep_name, import_path in dependencies.items():
        try:
            __import__(import_path)
            print(f"✅ {dep_name}")
        except ImportError as e:
            print(f"❌ {dep_name}: {e}")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n⚠️ Missing dependencies: {missing_deps}")
        return False
    
    print("✅ All chart dependencies available")
    return True

def test_chart_creation():
    """Test chart creation with minimal data"""
    print("\n🧪 Testing Chart Creation")
    print("=" * 30)
    
    try:
        # Create minimal test data
        print("📊 Creating minimal test data...")
        dates = pd.date_range(start=datetime.now() - timedelta(hours=5), periods=50, freq='5min')
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(22000, 22100, 50),
            'high': np.random.uniform(22050, 22150, 50),
            'low': np.random.uniform(21950, 22050, 50),
            'close': np.random.uniform(22000, 22100, 50),
            'volume': np.random.randint(10000, 50000, 50)
        })
        
        # Ensure high >= low, open/close within range
        test_data['high'] = np.maximum(test_data['high'], test_data[['open', 'close']].max(axis=1))
        test_data['low'] = np.minimum(test_data['low'], test_data[['open', 'close']].min(axis=1))
        
        print(f"✅ Created test data: {len(test_data)} candles")
        
        # Test basic plotly chart
        print("📈 Testing basic Plotly chart...")
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots
        
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.02,
            row_heights=[0.8, 0.2]
        )
        
        # Add candlestick
        fig.add_trace(
            go.Candlestick(
                x=test_data['timestamp'],
                open=test_data['open'],
                high=test_data['high'],
                low=test_data['low'],
                close=test_data['close'],
                name="Test",
                showlegend=False
            ),
            row=1, col=1
        )
        
        # Add volume
        fig.add_trace(
            go.Bar(
                x=test_data['timestamp'],
                y=test_data['volume'],
                name="Volume",
                marker_color='blue',
                opacity=0.6,
                showlegend=False
            ),
            row=2, col=1
        )
        
        # Update layout
        fig.update_layout(
            title="Test Chart",
            height=600,
            xaxis_rangeslider_visible=False,
            showlegend=False
        )
        
        print("✅ Basic Plotly chart created successfully")
        
        # Save test chart
        fig.write_html("test_basic_chart.html")
        print("💾 Saved as 'test_basic_chart.html'")
        
        return True
        
    except Exception as e:
        print(f"❌ Chart creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zerodha_chart_function():
    """Test the actual Zerodha chart function"""
    print("\n🧪 Testing Zerodha Chart Function")
    print("=" * 40)
    
    try:
        # Import the function
        from utils.chart_utils import create_zerodha_style_chart
        
        # Get test data
        from live_dashboard import get_simulated_data
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is None or candles.empty:
            print("❌ No test data available")
            return False
        
        print(f"📊 Using {len(candles)} candles for test")
        
        # Test with minimal parameters
        print("📈 Creating Zerodha-style chart...")
        fig = create_zerodha_style_chart(
            candles.tail(50),
            patterns=[],
            support_levels=[],
            resistance_levels=[],
            ml_prediction=None,
            show_indicators=False
        )
        
        if fig is None:
            print("❌ Chart function returned None")
            return False
        
        print("✅ Zerodha chart created successfully")
        
        # Save test chart
        fig.write_html("test_zerodha_function.html")
        print("💾 Saved as 'test_zerodha_function.html'")
        
        return True
        
    except Exception as e:
        print(f"❌ Zerodha chart function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indicators():
    """Test indicator calculation"""
    print("\n🧪 Testing Indicator Calculation")
    print("=" * 35)
    
    try:
        # Import functions
        from utils.indicators import calculate_indicators
        from live_dashboard import get_simulated_data
        
        # Get test data
        candles = get_simulated_data("NIFTY", "5minute")
        
        if candles is None or candles.empty:
            print("❌ No test data available")
            return False
        
        print(f"📊 Testing indicators on {len(candles)} candles")
        
        # Calculate indicators
        candles_with_indicators = calculate_indicators(candles)
        
        # Check if indicators were added
        indicator_cols = ['RSI', 'MACD', 'VWAP', 'EMA_12', 'SMA_20']
        present_indicators = [col for col in indicator_cols if col in candles_with_indicators.columns]
        
        print(f"✅ Indicators calculated: {present_indicators}")
        
        # Check for excessive NaN values
        for col in present_indicators:
            nan_count = candles_with_indicators[col].isna().sum()
            total_count = len(candles_with_indicators)
            nan_percentage = (nan_count / total_count) * 100
            
            if nan_percentage > 50:
                print(f"⚠️ {col}: {nan_percentage:.1f}% NaN values")
            else:
                print(f"✅ {col}: {nan_percentage:.1f}% NaN values")
        
        return True
        
    except Exception as e:
        print(f"❌ Indicator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function"""
    print("🔍 CHART LOADING ISSUE DIAGNOSIS")
    print("=" * 50)
    
    # Run all tests
    test_results = {
        'Data Format': test_data_format(),
        'Chart Dependencies': test_chart_dependencies(),
        'Chart Creation': test_chart_creation(),
        'Zerodha Function': test_zerodha_chart_function(),
        'Indicators': test_indicators()
    }
    
    # Summary
    print(f"\n📊 DIAGNOSTIC SUMMARY:")
    print("=" * 30)
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    # Recommendations
    failed_tests = [name for name, result in test_results.items() if not result]
    
    if not failed_tests:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   The chart should work correctly.")
        print(f"   Check the generated HTML files to verify chart rendering.")
    else:
        print(f"\n⚠️ ISSUES FOUND:")
        for test in failed_tests:
            print(f"   • {test}")
        
        print(f"\n🔧 RECOMMENDED FIXES:")
        if 'Chart Dependencies' in failed_tests:
            print("   • Install missing dependencies: pip install plotly mplfinance")
        if 'Data Format' in failed_tests:
            print("   • Check data source and format")
        if 'Zerodha Function' in failed_tests:
            print("   • Check chart_utils.py for errors")
        if 'Indicators' in failed_tests:
            print("   • Check indicators.py for calculation errors")

if __name__ == "__main__":
    main()
