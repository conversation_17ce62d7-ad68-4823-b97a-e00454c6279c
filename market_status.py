#!/usr/bin/env python3
"""
Market Status Checker
Determines if markets are open and provides appropriate data handling
"""

import datetime
import pytz
import requests
import logging
from kite_auth_helper import get_kite_connection

logger = logging.getLogger(__name__)

def get_market_status():
    """Get current market status from Kite API"""
    try:
        kite = get_kite_connection()
        if kite:
            # Try to get market status from Kite API
            # Note: This might not be available in all Kite versions
            try:
                profile = kite.profile()
                # Market status is not directly available, so we'll calculate it
                return calculate_market_status()
            except:
                return calculate_market_status()
        else:
            return calculate_market_status()
    except Exception as e:
        logger.error(f"Error getting market status: {e}")
        return calculate_market_status()

def calculate_market_status():
    """Calculate market status based on time and day"""
    # Indian market timezone
    ist = pytz.timezone('Asia/Kolkata')
    now = datetime.datetime.now(ist)
    
    # Market timings (IST)
    market_open_time = datetime.time(9, 15)  # 9:15 AM
    market_close_time = datetime.time(15, 30)  # 3:30 PM
    
    # Check if it's a weekend
    if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return {
            'status': 'closed',
            'reason': 'Weekend',
            'next_open': get_next_market_open(now),
            'is_live': False
        }
    
    # Check if market is open during weekdays
    current_time = now.time()
    
    if market_open_time <= current_time <= market_close_time:
        return {
            'status': 'open',
            'reason': 'Market hours',
            'next_close': datetime.datetime.combine(now.date(), market_close_time),
            'is_live': True
        }
    else:
        if current_time < market_open_time:
            reason = 'Pre-market'
            next_event = datetime.datetime.combine(now.date(), market_open_time)
        else:
            reason = 'Post-market'
            next_event = get_next_market_open(now)
        
        return {
            'status': 'closed',
            'reason': reason,
            'next_open': next_event,
            'is_live': False
        }

def get_next_market_open(current_time):
    """Get the next market opening time"""
    ist = pytz.timezone('Asia/Kolkata')
    
    # If it's Friday after market hours or weekend, next open is Monday
    if current_time.weekday() == 4 and current_time.time() > datetime.time(15, 30):
        # Friday after market close
        days_ahead = 3  # Monday
    elif current_time.weekday() >= 5:
        # Weekend
        days_ahead = 7 - current_time.weekday()  # Days until Monday
    else:
        # Weekday - next day
        days_ahead = 1
    
    next_open_date = current_time.date() + datetime.timedelta(days=days_ahead)
    next_open_datetime = datetime.datetime.combine(
        next_open_date, 
        datetime.time(9, 15)
    )
    
    return ist.localize(next_open_datetime)

def is_market_holiday():
    """Check if today is a market holiday (basic implementation)"""
    # This is a simplified version. In production, you'd want to maintain
    # a proper holiday calendar or use an API
    
    ist = pytz.timezone('Asia/Kolkata')
    today = datetime.datetime.now(ist).date()
    
    # Common holidays (you can expand this list)
    holidays_2024 = [
        datetime.date(2024, 1, 26),  # Republic Day
        datetime.date(2024, 3, 8),   # Holi
        datetime.date(2024, 3, 29),  # Good Friday
        datetime.date(2024, 8, 15),  # Independence Day
        datetime.date(2024, 10, 2),  # Gandhi Jayanti
        datetime.date(2024, 11, 1),  # Diwali
        # Add more holidays as needed
    ]
    
    return today in holidays_2024

def get_market_status_display():
    """Get market status for display in dashboard"""
    status = get_market_status()
    
    if status['is_live']:
        return {
            'emoji': '🟢',
            'text': 'Market Open',
            'color': 'green',
            'details': f"Open until {status.get('next_close', 'N/A').strftime('%H:%M')}"
        }
    else:
        next_open = status.get('next_open')
        if next_open:
            next_open_str = next_open.strftime('%a %d %b, %H:%M')
        else:
            next_open_str = 'Unknown'
            
        return {
            'emoji': '🔴',
            'text': f"Market Closed ({status['reason']})",
            'color': 'red',
            'details': f"Next open: {next_open_str}"
        }

def should_use_live_data():
    """Determine if we should use live data or demo data"""
    status = get_market_status()
    return status['is_live']

def get_data_source_info():
    """Get information about current data source"""
    if should_use_live_data():
        return {
            'source': 'Live Market Data',
            'description': 'Real-time data from NSE/BSE',
            'reliability': 'High',
            'update_frequency': '1-2 seconds'
        }
    else:
        return {
            'source': 'Demo/Historical Data',
            'description': 'Simulated data for demonstration',
            'reliability': 'Demo only',
            'update_frequency': 'Simulated real-time'
        }

if __name__ == "__main__":
    # Test the market status
    status = get_market_status()
    display = get_market_status_display()
    data_source = get_data_source_info()
    
    print(f"🕒 Market Status: {display['emoji']} {display['text']}")
    print(f"📊 Details: {display['details']}")
    print(f"💾 Data Source: {data_source['source']}")
    print(f"📝 Description: {data_source['description']}")
    print(f"🔄 Update Frequency: {data_source['update_frequency']}")
    
    if status['is_live']:
        print("\n✅ Using live market data")
    else:
        print(f"\n⚠️ Market is closed ({status['reason']})")
        print("📊 Dashboard will show demo data for demonstration")
