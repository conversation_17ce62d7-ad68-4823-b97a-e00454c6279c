#!/usr/bin/env python3
"""
Kite Authentication Helper
Handles proper Kite Connect API authentication with redirect URL
"""

import os
import json
import logging
from kiteconnect import KiteConnect
import streamlit as st

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Kite API credentials (update these with your actual values)
API_KEY = "gowlwfjssaue6pjf"  # From access_token.py
API_SECRET = "x7wf56gcgy2r3lgjeeexn6cz665i8xsz"  # From access_token.py
REDIRECT_URL = "http://localhost:8501"  # This should match your Kite app settings

def get_login_url():
    """Get the Kite login URL for authentication"""
    kite = KiteConnect(api_key=API_KEY)
    login_url = kite.login_url()
    return login_url

def generate_session(request_token):
    """Generate session using request token from redirect"""
    try:
        kite = KiteConnect(api_key=API_KEY)
        data = kite.generate_session(request_token, api_secret=API_SECRET)
        
        access_token = data["access_token"]
        
        # Save credentials
        credentials = {
            "api_key": API_KEY,
            "access_token": access_token,
            "user_id": data.get("user_id"),
            "user_name": data.get("user_name")
        }
        
        with open("access_token.json", "w") as f:
            json.dump(credentials, f, indent=2)
        
        logger.info(f"Session generated successfully for user: {data.get('user_name')}")
        return access_token
        
    except Exception as e:
        logger.error(f"Error generating session: {e}")
        return None

def get_kite_connection():
    """Get authenticated Kite connection with improved error handling"""
    try:
        # Check if we have valid access token
        if os.path.exists("access_token.json"):
            with open("access_token.json", "r") as f:
                credentials = json.load(f)

            kite = KiteConnect(api_key=credentials["api_key"])
            kite.set_access_token(credentials["access_token"])

            # Test the connection (only once to avoid spam)
            try:
                profile = kite.profile()
                logger.info(f"Connected to Kite API for user: {profile.get('user_name', 'Unknown')}")
                return kite
            except Exception as e:
                # Log warning only once, then return None to use demo mode
                logger.warning(f"Access token invalid: {e}")
                logger.info("Switching to demo mode with saved historical data")
                return None

        return None

    except Exception as e:
        logger.error(f"Error setting up Kite connection: {e}")
        return None

def handle_kite_auth_in_streamlit():
    """Handle Kite authentication within Streamlit app"""
    
    # Check URL parameters for request token
    query_params = st.query_params
    
    if "request_token" in query_params:
        request_token = query_params["request_token"]
        
        if "access_token" not in st.session_state:
            with st.spinner("Authenticating with Kite..."):
                access_token = generate_session(request_token)
                
                if access_token:
                    st.session_state.access_token = access_token
                    st.success("✅ Successfully authenticated with Kite!")
                    st.rerun()
                else:
                    st.error("❌ Authentication failed. Please try again.")
                    return None
    
    # Try to get existing connection
    kite = get_kite_connection()
    
    if kite:
        return kite
    
    # Show login button if not authenticated
    st.warning("🔐 Kite authentication required for live data")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔗 Login with Kite", type="primary"):
            login_url = get_login_url()
            st.markdown(f"""
            **Please follow these steps:**
            1. Click the link below to login to Kite
            2. After login, you'll be redirected back to this page
            3. The dashboard will automatically connect to live data
            
            [🔗 **Login to Kite**]({login_url})
            """)
    
    with col2:
        if st.button("📊 Use Demo Mode"):
            st.info("Using simulated data for demonstration")
            return None
    
    return None

def test_kite_connection():
    """Test the Kite connection"""
    kite = get_kite_connection()
    
    if kite:
        try:
            # Test basic API call
            profile = kite.profile()
            print(f"✅ Connected to Kite API")
            print(f"User: {profile.get('user_name')}")
            print(f"User ID: {profile.get('user_id')}")
            
            # Test market data
            quote = kite.quote("NSE:NIFTY 50")
            print(f"NIFTY 50 LTP: {quote['NSE:NIFTY 50']['last_price']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Kite API test failed: {e}")
            return False
    else:
        print("❌ No Kite connection available")
        return False

if __name__ == "__main__":
    # Test the connection
    if test_kite_connection():
        print("🎉 Kite authentication is working!")
    else:
        print("⚠️ Please authenticate with Kite first")
        print(f"Login URL: {get_login_url()}")
