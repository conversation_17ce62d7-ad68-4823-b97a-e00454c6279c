#!/usr/bin/env python3
"""
Daily Kite Token Generator
Simple script to generate daily access tokens for Kite API
"""

import json
import webbrowser
from kiteconnect import KiteConnect

# Your Kite API credentials
API_KEY = "gowlwfjssaue6pjf"
API_SECRET = "x7wf56gcgy2r3lgjeeexn6cz665i8xsz"

def generate_daily_token():
    """Generate daily access token for Kite API"""
    
    print("🔐 KITE API DAILY TOKEN GENERATOR")
    print("=" * 50)
    
    # Step 1: Create Kite connection
    kite = KiteConnect(api_key=API_KEY)
    
    # Step 2: Get login URL
    login_url = kite.login_url()
    print(f"📱 Step 1: Login URL generated")
    print(f"🔗 URL: {login_url}")
    
    # Step 3: Open browser automatically
    print(f"\n🌐 Step 2: Opening browser automatically...")
    try:
        webbrowser.open(login_url)
        print("✅ Browser opened successfully")
    except:
        print("⚠️ Could not open browser automatically")
        print("Please copy and paste the URL above into your browser")
    
    # Step 4: Get request token from user
    print(f"\n📋 Step 3: After login, you'll be redirected to a URL")
    print("The URL might look like one of these:")
    print("• http://localhost:8501?request_token=XXXXXX&action=login&status=success")
    print("• http://127.0.0.1:8501?request_token=XXXXXX&action=login&status=success")
    print("• Or just copy the ENTIRE URL if it contains 'request_token'")
    print("\n💡 TIPS:")
    print("• If localhost doesn't work, copy the FULL URL from address bar")
    print("• Look for 'request_token=' in the URL")
    print("• The token is the long string after 'request_token='")

    user_input = input("\n🔑 Enter request token OR full URL: ").strip()

    if not user_input:
        print("❌ No input provided!")
        return False

    # Extract request token from input (handle both token and full URL)
    request_token = user_input
    if "request_token=" in user_input:
        # Extract token from URL
        try:
            import urllib.parse as urlparse
            parsed_url = urlparse.urlparse(user_input)
            query_params = urlparse.parse_qs(parsed_url.query)
            request_token = query_params.get('request_token', [None])[0]
            print(f"✅ Extracted token from URL: {request_token[:10]}...")
        except:
            print("⚠️ Could not parse URL, trying as direct token...")

    if not request_token:
        print("❌ No request token found!")
        return False
    
    # Step 5: Generate session
    try:
        print(f"\n🔄 Step 4: Generating access token...")
        data = kite.generate_session(request_token, api_secret=API_SECRET)
        
        access_token = data["access_token"]
        user_name = data.get("user_name", "Unknown")
        user_id = data.get("user_id", "Unknown")
        
        # Step 6: Save credentials
        credentials = {
            "api_key": API_KEY,
            "access_token": access_token,
            "user_id": user_id,
            "user_name": user_name,
            "generated_date": str(data.get("login_time", "Unknown"))
        }
        
        with open("access_token.json", "w") as f:
            json.dump(credentials, f, indent=2)
        
        print(f"✅ SUCCESS! Token generated successfully")
        print(f"👤 User: {user_name}")
        print(f"🆔 User ID: {user_id}")
        print(f"💾 Token saved to: access_token.json")
        
        # Step 7: Test the token
        print(f"\n🧪 Step 5: Testing the token...")
        kite.set_access_token(access_token)
        profile = kite.profile()
        
        print(f"✅ Token test successful!")
        print(f"📊 Account: {profile.get('user_name')}")
        print(f"📧 Email: {profile.get('email')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating token: {e}")
        return False

def check_existing_token():
    """Check if existing token is still valid"""
    try:
        with open("access_token.json", "r") as f:
            credentials = json.load(f)
        
        kite = KiteConnect(api_key=credentials["api_key"])
        kite.set_access_token(credentials["access_token"])
        
        profile = kite.profile()
        print(f"✅ Existing token is valid!")
        print(f"👤 User: {profile.get('user_name')}")
        return True
        
    except FileNotFoundError:
        print("📄 No existing token file found")
        return False
    except Exception as e:
        print(f"❌ Existing token invalid: {e}")
        return False

def main():
    """Main function"""
    print("🚀 KITE API TOKEN MANAGER")
    print("=" * 40)
    
    # Check existing token first
    print("🔍 Checking existing token...")
    if check_existing_token():
        choice = input("\n❓ Token is valid. Generate new one anyway? (y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("✅ Using existing token")
            return
    
    # Generate new token
    print("\n🔄 Generating new token...")
    if generate_daily_token():
        print("\n🎉 SUCCESS! You can now run the trading dashboard")
        print("💡 Run: python start.py")
    else:
        print("\n❌ Token generation failed. Please try again.")

if __name__ == "__main__":
    main()
