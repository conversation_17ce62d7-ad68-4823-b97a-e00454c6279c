import pandas as pd
import numpy as np
from scipy.signal import find_peaks

def calculate_indicators(df):
    """Calculate all technical indicators"""
    # Moving averages
    df["EMA_12"] = df["close"].ewm(span=12, adjust=False).mean()
    df["EMA_26"] = df["close"].ewm(span=26, adjust=False).mean()
    df["SMA_20"] = df["close"].rolling(window=20).mean()
    df["SMA_50"] = df["close"].rolling(window=50).mean()

    # MACD
    df["MACD"] = df["EMA_12"] - df["EMA_26"]
    df["MACD_signal"] = df["MACD"].ewm(span=9, adjust=False).mean()
    df["MACD_histogram"] = df["MACD"] - df["MACD_signal"]

    # RSI
    df["RSI"] = compute_rsi(df["close"], 14)

    # VWAP (Volume Weighted Average Price) - Fixed calculation
    typical_price = (df["high"] + df["low"] + df["close"]) / 3
    volume_price = typical_price * df["volume"]
    cumulative_volume_price = volume_price.cumsum()
    cumulative_volume = df["volume"].cumsum()

    # Avoid division by zero
    df["VWAP"] = np.where(cumulative_volume != 0,
                          cumulative_volume_price / cumulative_volume,
                          typical_price)

    # Bollinger Bands
    df["BB_middle"] = df["close"].rolling(window=20).mean()
    bb_std = df["close"].rolling(window=20).std()
    df["BB_upper"] = df["BB_middle"] + (bb_std * 2)
    df["BB_lower"] = df["BB_middle"] - (bb_std * 2)

    # Stochastic
    df["Stoch_K"], df["Stoch_D"] = compute_stochastic(df, 14, 3, 3)

    # Average True Range (ATR)
    df["ATR"] = compute_atr(df, 14)

    return df

def compute_rsi(series, period=14):
    """Compute Relative Strength Index"""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def compute_stochastic(df, k_period=14, k_slowing=3, d_period=3):
    """Compute Stochastic Oscillator"""
    lowest_low = df["low"].rolling(window=k_period).min()
    highest_high = df["high"].rolling(window=k_period).max()

    k_percent = 100 * ((df["close"] - lowest_low) / (highest_high - lowest_low))
    k_percent_smooth = k_percent.rolling(window=k_slowing).mean()
    d_percent = k_percent_smooth.rolling(window=d_period).mean()

    return k_percent_smooth, d_percent

def compute_atr(df, period=14):
    """Compute Average True Range"""
    high_low = df["high"] - df["low"]
    high_close = np.abs(df["high"] - df["close"].shift())
    low_close = np.abs(df["low"] - df["close"].shift())

    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    return true_range.rolling(window=period).mean()

def calculate_pivot_points(df):
    """Calculate pivot points for support and resistance"""
    if len(df) < 1:
        return None

    # Use previous day's data for pivot calculation
    prev_high = df["high"].iloc[-1]
    prev_low = df["low"].iloc[-1]
    prev_close = df["close"].iloc[-1]

    # Pivot point
    pivot = (prev_high + prev_low + prev_close) / 3

    # Support levels
    s1 = (2 * pivot) - prev_high
    s2 = pivot - (prev_high - prev_low)
    s3 = prev_low - 2 * (prev_high - pivot)

    # Resistance levels
    r1 = (2 * pivot) - prev_low
    r2 = pivot + (prev_high - prev_low)
    r3 = prev_high + 2 * (pivot - prev_low)

    return {
        'pivot': pivot,
        'support': [s1, s2, s3],
        'resistance': [r1, r2, r3]
    }

def detect_trend(df, period=20):
    """Detect current trend direction"""
    if len(df) < period:
        return "Unknown"

    recent_data = df.tail(period)

    # Calculate trend using linear regression
    x = np.arange(len(recent_data))
    slope = np.polyfit(x, recent_data['close'].values, 1)[0]

    if slope > 0.1:
        return "Bullish"
    elif slope < -0.1:
        return "Bearish"
    else:
        return "Sideways"
