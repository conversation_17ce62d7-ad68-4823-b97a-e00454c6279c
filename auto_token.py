import pyotp
import requests
import json

# 🔒 Update with your actual details
user_id = "UPH285"
password = "<PERSON>yan@8855"
totp_secret = "VKQXSUU3U32KPQQTYEY7T6BAZQYRKVFN"  # From Kite TOTP setup

# Generate TOTP
totp = pyotp.TOTP(totp_secret).now()
print("🔐 Generated TOTP:", totp)

# Step 1: Start session and login
session = requests.Session()
login_resp = session.post("https://kite.zerodha.com/api/login", data={
    "user_id": user_id,
    "password": password
})

if login_resp.status_code != 200 or "data" not in login_resp.json():
    raise Exception("❌ Login failed at password step")

request_id = login_resp.json()["data"]["request_id"]

# Step 2: Submit TOTP
twofa_resp = session.post("https://kite.zerodha.com/api/twofa", data={
    "user_id": user_id,
    "request_id": request_id,
    "twofa_value": totp
})

if "enctoken" not in twofa_resp.cookies:
    raise Exception("❌ TOTP/2FA failed")

# Step 3: Save the enctoken for reuse
enctoken = twofa_resp.cookies["enctoken"]
with open("enctoken.txt", "w") as f:
    f.write(enctoken)

print("✅ Login successful! enctoken saved to enctoken.txt")
