#!/usr/bin/env python3
"""
Automatic Kite Token Generator with Local Server
This script automatically captures the token without manual copying
"""

import json
import webbrowser
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from kiteconnect import KiteConnect

# Your Kite API credentials
API_KEY = "gowlwfjssaue6pjf"
API_SECRET = "x7wf56gcgy2r3lgjeeexn6cz665i8xsz"

# Global variable to store captured token
captured_token = None
server_running = False

class TokenCaptureHandler(BaseHTTPRequestHandler):
    """HTTP handler to capture the request token"""
    
    def do_GET(self):
        global captured_token, server_running
        
        # Parse the URL and query parameters
        parsed_path = urlparse(self.path)
        query_params = parse_qs(parsed_path.query)
        
        # Check if we have a request token
        if 'request_token' in query_params:
            captured_token = query_params['request_token'][0]
            
            # Send success response
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            success_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Kite Token Captured</title>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f8ff; }}
                    .success {{ color: #28a745; font-size: 24px; margin: 20px; }}
                    .token {{ background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>🎉 Token Captured Successfully!</h1>
                <div class="success">✅ Authentication completed</div>
                <p>Request Token: <span class="token">{captured_token}</span></p>
                <p>You can close this window and return to the terminal.</p>
                <script>
                    setTimeout(function() {{
                        window.close();
                    }}, 3000);
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(success_html.encode())
            print(f"\n✅ Token captured: {captured_token[:10]}...")
            
            # Stop the server
            threading.Timer(1.0, self.server.shutdown).start()
            
        else:
            # Send error response
            self.send_response(400)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Token Not Found</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #fff5f5; }
                    .error { color: #dc3545; font-size: 24px; margin: 20px; }
                </style>
            </head>
            <body>
                <h1>❌ Token Not Found</h1>
                <div class="error">No request token in URL</div>
                <p>Please try the authentication process again.</p>
            </body>
            </html>
            """
            
            self.wfile.write(error_html.encode())
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

def start_token_server():
    """Start local server to capture token"""
    global server_running
    
    try:
        server = HTTPServer(('localhost', 8501), TokenCaptureHandler)
        server_running = True
        print("🌐 Local server started on http://localhost:8501")
        server.serve_forever()
    except Exception as e:
        print(f"❌ Could not start server: {e}")
        return False

def generate_token_automatically():
    """Generate token with automatic capture"""
    global captured_token, server_running
    
    print("🤖 AUTOMATIC KITE TOKEN GENERATOR")
    print("=" * 50)
    
    # Step 1: Start local server in background
    print("🌐 Step 1: Starting local server...")
    server_thread = threading.Thread(target=start_token_server, daemon=True)
    server_thread.start()
    
    # Wait for server to start
    time.sleep(2)
    
    if not server_running:
        print("❌ Could not start local server. Using manual method.")
        return manual_token_generation()
    
    # Step 2: Create Kite connection and get login URL
    kite = KiteConnect(api_key=API_KEY)
    login_url = kite.login_url()
    
    print(f"🔗 Step 2: Opening Kite login page...")
    print(f"URL: {login_url}")
    
    # Step 3: Open browser
    try:
        webbrowser.open(login_url)
        print("✅ Browser opened successfully")
    except:
        print("⚠️ Could not open browser automatically")
        print(f"Please open this URL manually: {login_url}")
    
    # Step 4: Wait for token capture
    print(f"\n⏳ Step 3: Waiting for authentication...")
    print("Please login in the browser window that opened.")
    print("The token will be captured automatically!")
    
    # Wait up to 120 seconds for token
    timeout = 120
    start_time = time.time()
    
    while captured_token is None and (time.time() - start_time) < timeout:
        time.sleep(1)
        remaining = int(timeout - (time.time() - start_time))
        if remaining % 10 == 0:  # Print every 10 seconds
            print(f"⏳ Waiting... ({remaining}s remaining)")
    
    if captured_token is None:
        print("❌ Timeout! Token not captured.")
        print("Please try manual method or check your internet connection.")
        return False
    
    # Step 5: Generate session
    try:
        print(f"\n🔄 Step 4: Generating access token...")
        data = kite.generate_session(captured_token, api_secret=API_SECRET)
        
        access_token = data["access_token"]
        user_name = data.get("user_name", "Unknown")
        user_id = data.get("user_id", "Unknown")
        
        # Save credentials
        credentials = {
            "api_key": API_KEY,
            "access_token": access_token,
            "user_id": user_id,
            "user_name": user_name,
            "generated_date": str(data.get("login_time", "Unknown"))
        }
        
        with open("access_token.json", "w") as f:
            json.dump(credentials, f, indent=2)
        
        print(f"✅ SUCCESS! Token generated automatically")
        print(f"👤 User: {user_name}")
        print(f"🆔 User ID: {user_id}")
        print(f"💾 Token saved to: access_token.json")
        
        # Test the token
        print(f"\n🧪 Step 5: Testing the token...")
        kite.set_access_token(access_token)
        profile = kite.profile()
        
        print(f"✅ Token test successful!")
        print(f"📊 Account: {profile.get('user_name')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating token: {e}")
        return False

def manual_token_generation():
    """Fallback to manual token generation"""
    print("\n🔄 Falling back to manual method...")
    
    # Import and use the existing manual method
    import generate_token
    return generate_token.generate_daily_token()

def main():
    """Main function"""
    print("🚀 AUTOMATIC KITE TOKEN GENERATOR")
    print("=" * 40)
    
    # Check existing token first
    print("🔍 Checking existing token...")
    try:
        with open("access_token.json", "r") as f:
            credentials = json.load(f)
        
        kite = KiteConnect(api_key=credentials["api_key"])
        kite.set_access_token(credentials["access_token"])
        profile = kite.profile()
        
        print(f"✅ Existing token is valid!")
        print(f"👤 User: {profile.get('user_name')}")
        
        choice = input("\n❓ Token is valid. Generate new one anyway? (y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("✅ Using existing token")
            return
            
    except:
        print("❌ No valid existing token found")
    
    # Generate new token
    print("\n🤖 Starting automatic token generation...")
    if generate_token_automatically():
        print("\n🎉 SUCCESS! You can now run the trading dashboard")
        print("💡 Run: python start.py")
    else:
        print("\n❌ Automatic generation failed.")
        print("💡 Try running: python generate_token.py for manual method")

if __name__ == "__main__":
    main()
