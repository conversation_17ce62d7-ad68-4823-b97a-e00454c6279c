#!/usr/bin/env python3
"""
Debug version of the dashboard startup
"""

import subprocess
import sys
import os
import time

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_modules = [
        'streamlit', 'pandas', 'numpy', 'plotly',
        'sklearn', 'joblib', 'kiteconnect'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {missing_modules}")
        print("Please install them using: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ All dependencies available")
    return True

def check_data_files():
    """Check if required data files exist"""
    print("\n🔍 Checking data files...")
    
    required_files = [
        'ml_models/advanced_catboost_classifier.pkl',
        'ml_models/advanced_catboost_scaler.pkl',
        'data/training_data_90days.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {missing_files}")
        print("The dashboard will use fallback data/models")
    else:
        print("✅ All data files available")
    
    return True

def test_basic_functionality():
    """Test basic functionality before starting dashboard"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test data generation
        print("📊 Testing data generation...")
        from live_dashboard import get_simulated_data
        candles = get_simulated_data("NIFTY", "5minute")
        if candles is not None and not candles.empty:
            print(f"✅ Data generation: {len(candles)} candles")
        else:
            print("❌ Data generation failed")
            return False
        
        # Test ML prediction
        print("🤖 Testing ML prediction...")
        from ml_models.predictor import predict_ce_pe
        prediction = predict_ce_pe(candles)
        if prediction and prediction.get('signal') != 'Unknown':
            print(f"✅ ML prediction: {prediction['signal']} ({prediction['confidence']:.1f}%)")
        else:
            print("❌ ML prediction failed")
            return False
        
        # Test chart creation
        print("📈 Testing chart creation...")
        from utils.chart_utils import create_zerodha_style_chart
        from pattern_engine import detect_patterns, calculate_support_resistance
        
        patterns = detect_patterns(candles)
        support_levels, resistance_levels = calculate_support_resistance(candles)
        
        fig = create_zerodha_style_chart(
            candles.tail(50),
            patterns=patterns,
            support_levels=support_levels,
            resistance_levels=resistance_levels,
            ml_prediction=prediction,
            show_indicators=True
        )
        
        if fig:
            print("✅ Chart creation successful")
        else:
            print("❌ Chart creation failed")
            return False
        
        print("✅ All basic functionality tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_dashboard():
    """Start the Streamlit dashboard"""
    print("\n🚀 Starting Live Trading Dashboard...")
    print("=" * 50)
    
    try:
        # Start streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "live_dashboard.py", "--server.port=8501"]
        
        print("📡 Dashboard will be available at: http://localhost:8501")
        print("🔄 Starting Streamlit server...")
        
        # Start the process
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # Wait a bit for startup
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Dashboard started successfully!")
            print("\n📋 DASHBOARD FEATURES:")
            print("   • Real-time candlestick charts (Zerodha-style)")
            print("   • ML-powered CE/PE predictions")
            print("   • Live market data integration")
            print("   • Pattern recognition")
            print("   • Support/resistance levels")
            print("   • Technical indicators")
            
            print("\n🎯 USAGE TIPS:")
            print("   • Click 'Refresh Chart' to load initial data")
            print("   • Enable 'Real-time Updates' for live data")
            print("   • Check sidebar for connection status")
            print("   • ML signals appear on the chart")
            
            print("\n⚠️ TROUBLESHOOTING:")
            print("   • If chart shows 'Initializing...', click refresh")
            print("   • If no candles appear, check data source")
            print("   • If ML shows 'Unknown', wait for data to load")
            
            # Keep the process running
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping dashboard...")
                process.terminate()
                process.wait()
                print("✅ Dashboard stopped")
        else:
            print("❌ Dashboard failed to start")
            output = process.stdout.read()
            print(f"Error output: {output}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start dashboard: {e}")
        return False

def main():
    """Main function"""
    print("🚀 LIVE TRADING DASHBOARD - DEBUG STARTUP")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing modules.")
        return
    
    # Check data files
    check_data_files()
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n❌ Basic functionality test failed.")
        print("The dashboard may not work correctly.")
        response = input("\nDo you want to start anyway? (y/n): ")
        if response.lower() != 'y':
            return
    
    # Start dashboard
    start_dashboard()

if __name__ == "__main__":
    main()
